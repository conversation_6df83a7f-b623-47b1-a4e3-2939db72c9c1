repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: check-toml
      - id: fix-byte-order-marker
      - id: check-yaml

  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.5.4
    hooks:
      - id: remove-crlf

  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.37.0
    hooks:
      - id: markdownlint
        args: [--config, .config/.markdownlintrc, --fix]

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.3
    hooks:
      - id: prettier

  - repo: https://github.com/streetsidesoftware/cspell-cli
    rev: v8.17.3
    hooks:
      - id: cspell # Spell check changed files
        name: check sol file spelling
        args:
          - "contracts/**/*.sol"
          - --locale=en
          - --exclude
          - ".pre-commit-config.yaml"
      - id: cspell # Spell check the commit message
        name: check commit message spelling
        types: [file]
        args:
          - --locale=en
          - --no-must-find-files
          - --no-progress
          - --no-summary
          - --files
          - .git/COMMIT_EDITMSG
        stages: [commit-msg]
        always_run: true # This might not be necessary.

  - repo: local
    hooks:
      - id: forge-fmt
        name: Forge fmt
        entry: forge fmt
        args: ["contracts"]
        language: system
        files: \.(sol)$

  - repo: https://github.com/crytic/slither
    rev: 16fd330723aca93dc3342dee77340e4b344dc732
    hooks:
      - id: slither
        name: slither clob
        args:
          [
            contracts/clob,
            --config-file,
            .config/.slither.config.json,
            --skip-assembly,
          ]
      - id: slither
        name: slither launchpad
        args:
          [
            contracts/launchpad,
            --config-file,
            .config/.slither.config.json,
            --skip-assembly,
          ]
      - id: slither
        name: slither router
        args:
          [
            contracts/router,
            --config-file,
            .config/.slither.config.json,
            --skip-assembly,
          ]
