// SPDX-License-Identifier: MIT

import "src/utils/EIP712.sol";

contract EIP712Mock is EIP712 {
    string constant public NAME = "Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Donec ipsum massa, ullamcorper in, auctor et, scelerisque sed, est. Maecenas libero. Phasellus et lorem id felis nonummy placerat. Vestibulum erat nulla, ullamcorper nec, rutrum non, nonummy ac, erat. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Etiam commodo dui eget wisi. Duis viverra diam non justo. Proin pede metus, vulputate nec, fermentum fringilla, vehicula vitae, justo. Mauris elementum mauris vitae tortor. Ut tempus purus at lorem.  Maecenas sollicitudin. Vivamus ac leo pretium faucibus. Vivamus luctus egestas leo. Proin in tellus sit amet nibh dignissim sagittis. Aliquam erat volutpat. Cras elementum. Curabitur ligula sapien, pulvinar a vestibulum quis, facilisis vel sapien. Fusce wisi. Suspendisse sagittis ultrices augue. Aenean id metus id velit ullamcorper pulvinar. Cras pede libero, dapibus nec, pretium sit amet, tempor quis. Cras elementum. Nunc auctor. Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur? Nulla est. Aliquam ornare wisi eu metus. Et harum quidem rerum facilis est et expedita distinctio. Mauris metus. Phasellus faucibus molestie nisl. Mauris tincidunt sem sed arcu.  Aenean placerat. Etiam neque. Nullam feugiat, turpis at pulvinar vulputate, erat libero tristique tellus, nec bibendum odio risus sit amet ante. Nullam dapibus fermentum ipsum. Mauris tincidunt sem sed arcu. Nam quis nulla. Nullam feugiat, turpis at pulvinar vulputate, erat libero tristique tellus, nec bibendum odio risus sit amet ante. Pellentesque sapien. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos hymenaeos. Vestibulum erat nulla, ullamcorper nec, rutrum non, nonummy ac, erat.  Maecenas aliquet accumsan leo. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos hymenaeos. Nullam rhoncus aliquam metus. Praesent vitae arcu tempor neque lacinia pretium. Nulla pulvinar eleifend sem. Etiam bibendum elit eget erat. Aenean placerat. Mauris metus. Aenean fermentum risus id tortor.  Suspendisse nisl. Fusce wisi. Fusce nibh. Sed vel lectus. Donec odio tempus molestie, porttitor ut, iaculis quis, sem. Curabitur ligula sapien, pulvinar a vestibulum quis, facilisis vel sapien. Mauris elementum mauris vitae tortor. Duis sapien nunc, commodo et, interdum suscipit, sollicitudin et, dolor. Quisque tincidunt scelerisque libero. Vivamus luctus egestas leo. Etiam dui sem, fermentum vitae, sagittis id, malesuada in, quam. Duis risus. Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Suspendisse nisl. Cras elementum. Phasellus rhoncus. Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus. In enim a arcu imperdiet malesuada. In dapibus augue non sapien.  ";
    string constant public VERSION = "1.0.0";

    function _domainNameAndVersion() internal pure override returns (string memory, string memory) {
        return (NAME, VERSION);
    }

    function hashTypedData(bytes32 structHash) external view returns(bytes32) {
        return _hashTypedData(structHash);
    }

}