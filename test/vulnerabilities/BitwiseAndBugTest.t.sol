// SPDX-License-Identifier: MIT
pragma solidity ^0.8.27;

import "forge-std/Test.sol";

/**
 * @title BitwiseAndBugTest
 * @notice Test to verify the bitwise AND bug in CLOB limit order validation
 * @dev This test demonstrates that the buggy condition can cause false positives
 */
contract BitwiseAndBugTest is Test {
    
    /**
     * @notice Test the basic bitwise AND bug scenario
     * @dev Demonstrates how valid non-zero amounts can trigger false revert
     */
    function testBitwiseAndBugBasic() public {
        // Example values that would cause false revert in CLOB
        uint256 baseTokenAmountSent = 5;      // Binary: 101
        uint256 quoteTokenAmountReceived = 2; // Binary: 010
        
        // Bitwise AND: 101 & 010 = 000 (equals 0)
        uint256 bitwiseResult = baseTokenAmountSent & quoteTokenAmountReceived;
        assertEq(bitwiseResult, 0, "Bitwise AND should be 0");
        
        // This is the buggy condition from CLOB.sol lines 503 and 544
        bool buggyCondition = (baseTokenAmountSent != quoteTokenAmountReceived) && 
                             (baseTokenAmountSent & quoteTokenAmountReceived == 0);
        assertTrue(buggyCondition, "Buggy condition triggers false positive");
        
        // But both amounts are non-zero (this should be a valid trade)
        assertTrue(baseTokenAmountSent > 0, "Base amount is non-zero");
        assertTrue(quoteTokenAmountReceived > 0, "Quote amount is non-zero");
        
        console.log("FALSE POSITIVE: Valid trade amounts %d and %d would be rejected", 
                   baseTokenAmountSent, quoteTokenAmountReceived);
    }
    
    /**
     * @notice Test multiple scenarios that trigger the bitwise AND bug
     * @dev Shows various amount combinations that would cause false reverts
     */
    function testMultipleBitwiseAndScenarios() public {
        // Array of amount pairs that trigger the bug
        uint256[2][] memory testCases = new uint256[2][](8);
        
        testCases[0] = [uint256(1), uint256(2)];   // 1 & 2 = 0
        testCases[1] = [uint256(1), uint256(4)];   // 1 & 4 = 0  
        testCases[2] = [uint256(2), uint256(1)];   // 2 & 1 = 0
        testCases[3] = [uint256(4), uint256(3)];   // 4 & 3 = 0
        testCases[4] = [uint256(8), uint256(7)];   // 8 & 7 = 0
        testCases[5] = [uint256(16), uint256(15)]; // 16 & 15 = 0
        testCases[6] = [uint256(32), uint256(31)]; // 32 & 31 = 0
        testCases[7] = [uint256(64), uint256(63)]; // 64 & 63 = 0
        
        for (uint i = 0; i < testCases.length; i++) {
            uint256 base = testCases[i][0];
            uint256 quote = testCases[i][1];
            
            // Verify bitwise AND is zero
            assertEq(base & quote, 0, string(abi.encodePacked("Bitwise AND should be 0 for case ", vm.toString(i))));
            
            // This would trigger the buggy condition (false revert)
            bool buggyCondition = (base != quote) && (base & quote == 0);
            assertTrue(buggyCondition, string(abi.encodePacked("False positive for case ", vm.toString(i))));
            
            // But both amounts are valid (non-zero)
            assertTrue(base > 0 && quote > 0, string(abi.encodePacked("Valid amounts for case ", vm.toString(i))));
            
            console.log("Case %d: amounts %d and %d would be falsely rejected", i, base, quote);
        }
    }
    
    /**
     * @notice Test realistic DeFi amounts that would trigger the bug
     * @dev Uses actual token amounts that could occur in real trading
     */
    function testRealisticDeFiAmounts() public {
        // Realistic amounts in wei that would trigger the bug
        uint256 oneEth = 1 ether;
        uint256 twoEth = 2 ether;
        uint256 fourEth = 4 ether;
        uint256 threeEth = 3 ether;
        
        // Test case: 1 ETH base, 2 ETH quote equivalent
        assertTrue((oneEth & twoEth) == 0, "1 ETH & 2 ETH = 0");
        bool wouldRevert1 = (oneEth != twoEth) && (oneEth & twoEth == 0);
        assertTrue(wouldRevert1, "1 ETH vs 2 ETH would falsely revert");
        
        // Test case: 4 ETH base, 3 ETH quote equivalent  
        assertTrue((fourEth & threeEth) == 0, "4 ETH & 3 ETH = 0");
        bool wouldRevert2 = (fourEth != threeEth) && (fourEth & threeEth == 0);
        assertTrue(wouldRevert2, "4 ETH vs 3 ETH would falsely revert");
        
        console.log("REALISTIC SCENARIO: 1 ETH trade would be falsely rejected");
        console.log("REALISTIC SCENARIO: 4 ETH vs 3 ETH trade would be falsely rejected");
    }
    
    /**
     * @notice Test the correct logic that should be used instead
     * @dev Shows how the condition should be written to avoid false positives
     */
    function testCorrectLogic() public {
        uint256 baseTokenAmountSent = 5;
        uint256 quoteTokenAmountReceived = 2;
        
        // Current buggy logic
        bool buggyLogic = (baseTokenAmountSent != quoteTokenAmountReceived) && 
                         (baseTokenAmountSent & quoteTokenAmountReceived == 0);
        assertTrue(buggyLogic, "Buggy logic triggers false positive");
        
        // Correct logic should check if BOTH are zero (actual zero cost trade)
        bool correctLogic = (baseTokenAmountSent == 0) && (quoteTokenAmountReceived == 0);
        assertFalse(correctLogic, "Correct logic does not trigger false positive");
        
        // Alternative correct logic
        bool alternativeCorrect = (baseTokenAmountSent == 0) || (quoteTokenAmountReceived == 0);
        assertFalse(alternativeCorrect, "Alternative correct logic does not trigger false positive");
        
        console.log("SOLUTION: Use logical checks for zero amounts, not bitwise operations");
    }
    
    /**
     * @notice Demonstrate the impact on gas costs
     * @dev Shows that users would lose gas on failed transactions
     */
    function testGasImpact() public {
        uint256 gasStart = gasleft();
        
        // Simulate the buggy condition check
        uint256 base = 1 ether;
        uint256 quote = 2 ether;
        
        bool wouldRevert = (base != quote) && (base & quote == 0);
        
        uint256 gasUsed = gasStart - gasleft();
        
        if (wouldRevert) {
            console.log("Transaction would revert after using %d gas", gasUsed);
            console.log("User loses gas fees for valid trade attempt");
        }
        
        assertTrue(wouldRevert, "Valid trade would waste user's gas");
    }
}
