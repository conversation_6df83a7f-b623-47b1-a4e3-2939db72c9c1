// SPDX-License-Identifier: MIT
pragma solidity ^0.8.27;

import {PoCTestBase} from "test/c4-poc/PoCTestBase.t.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {CLOB} from "contracts/clob/CLOB.sol";
import {Side} from "contracts/clob/types/Order.sol";
import {console} from "forge-std/console.sol";

/**
 * @title BitwiseAndBugVerification
 * @notice Targeted test to verify the bitwise AND bug in limit order validation
 * @dev This test creates specific scenarios that trigger the bitwise AND bug
 */
contract BitwiseAndBugVerification is PoCTestBase {
    
    CLOB public testClob;
    address public alice;
    address public bob;
    
    function setUp() public override {
        super.setUp();

        alice = makeAddr("alice");
        bob = makeAddr("bob");

        // Use existing CLOB from PoCTestBase
        testClob = CLOB(address(abCLOB));

        // Setup balances and deposits
        _setupUserBalances(alice, 100000 * 1e18, 1000 * 1e18);
        _setupUserBalances(bob, 100000 * 1e18, 1000 * 1e18);
    }
    
    function _setupUserBalances(address user, uint256 quoteAmount, uint256 baseAmount) internal {
        // Mint tokens (using tokenA and tokenB from PoCTestBase)
        tokenA.mint(user, quoteAmount);
        tokenB.mint(user, baseAmount);

        // Approve account manager
        vm.startPrank(user);
        tokenA.approve(address(accountManager), type(uint256).max);
        tokenB.approve(address(accountManager), type(uint256).max);

        // Deposit to account manager
        accountManager.deposit(user, address(tokenA), quoteAmount / 2);
        accountManager.deposit(user, address(tokenB), baseAmount / 2);
        vm.stopPrank();
    }
    
    /**
     * @notice Test the bitwise AND bug with carefully crafted amounts
     * @dev Creates a scenario where valid trade amounts trigger the bug
     */
    function testBitwiseAndBugWithCraftedAmounts() public {
        console.log("=== TESTING BITWISE AND BUG WITH CRAFTED AMOUNTS ===");
        
        // Step 1: Alice places a limit sell order for a specific amount
        vm.prank(alice);
        ICLOB.PostLimitOrderArgs memory aliceArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: 1 * 1e18, // 1 ETH
            price: 3000 * 1e18, // 3000 USDC per ETH
            cancelTimestamp: 0,
            side: Side.SELL,
            clientOrderId: 1,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
        });
        
        ICLOB.PostLimitOrderResult memory aliceResult = testClob.postLimitOrder(alice, aliceArgs);
        console.log("Alice placed sell order: 1 ETH at 3000 USDC");
        console.log("Alice order ID: %d", aliceResult.orderId);
        
        // Step 2: Bob places a limit buy order that will partially match
        // We need to create amounts where baseTokenAmountReceived & quoteTokenAmountSent == 0
        // but both are non-zero
        
        vm.prank(bob);
        ICLOB.PostLimitOrderArgs memory bobArgs = ICLOB.PostLimitOrderArgs({
            amountInBase: 2 * 1e18, // 2 ETH - will partially match with Alice's 1 ETH
            price: 3000 * 1e18, // Same price to ensure matching
            cancelTimestamp: 0,
            side: Side.BUY,
            clientOrderId: 2,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
        });
        
        console.log("Bob attempting to place buy order: 2 ETH at 3000 USDC");
        console.log("This should partially match with Alice's order");
        
        // This should trigger the bitwise AND bug if the amounts work out correctly
        try testClob.postLimitOrder(bob, bobArgs) returns (ICLOB.PostLimitOrderResult memory bobResult) {
            console.log("Bob's order succeeded");
            console.log("Bob order ID: %d", bobResult.orderId);
            console.log("Bob base traded: %d", uint256(bobResult.baseTokenAmountTraded));
            console.log("Bob quote traded: %d", uint256(bobResult.quoteTokenAmountTraded));
            console.log("Bob amount posted: %d", bobResult.amountPostedInBase);
            
            // Check if we can find the bitwise AND condition
            uint256 baseTraded = uint256(bobResult.baseTokenAmountTraded);
            uint256 quoteTraded = uint256(-bobResult.quoteTokenAmountTraded); // Make positive
            
            console.log("Checking bitwise AND condition:");
            console.log("Base traded: %d", baseTraded);
            console.log("Quote traded: %d", quoteTraded);
            console.log("Bitwise AND result: %d", baseTraded & quoteTraded);
            
            if ((baseTraded & quoteTraded) == 0 && baseTraded != quoteTraded && baseTraded > 0 && quoteTraded > 0) {
                console.log("POTENTIAL VULNERABILITY: Found amounts that would trigger bitwise AND bug");
                console.log("baseTraded != quoteTraded: %s", baseTraded != quoteTraded ? "true" : "false");
                console.log("baseTraded & quoteTraded == 0: %s", (baseTraded & quoteTraded) == 0 ? "true" : "false");
            }
            
        } catch Error(string memory reason) {
            console.log("Bob's order failed with reason: %s", reason);
            
            if (keccak256(bytes(reason)) == keccak256(bytes("ZeroCostTrade()"))) {
                console.log("VULNERABILITY CONFIRMED: ZeroCostTrade() error due to bitwise AND bug");
            }
        }
    }
    
    /**
     * @notice Test with specific amounts known to trigger bitwise AND == 0
     * @dev Uses amounts where bitwise AND equals zero but both are non-zero
     */
    function testSpecificBitwiseAndAmounts() public {
        console.log("=== TESTING SPECIFIC BITWISE AND AMOUNTS ===");
        
        // Test with amounts that we know have bitwise AND == 0
        uint256[] memory testAmounts1 = new uint256[](4);
        uint256[] memory testAmounts2 = new uint256[](4);
        
        testAmounts1[0] = 1 * 1e18; testAmounts2[0] = 2 * 1e18; // 1 & 2 = 0
        testAmounts1[1] = 4 * 1e18; testAmounts2[1] = 3 * 1e18; // 4 & 3 = 0
        testAmounts1[2] = 8 * 1e18; testAmounts2[2] = 7 * 1e18; // 8 & 7 = 0
        testAmounts1[3] = 16 * 1e18; testAmounts2[3] = 15 * 1e18; // 16 & 15 = 0
        
        for (uint i = 0; i < testAmounts1.length; i++) {
            uint256 amount1 = testAmounts1[i];
            uint256 amount2 = testAmounts2[i];
            
            console.log("\n--- Test case %d ---", i + 1);
            console.log("Amount1: %d", amount1);
            console.log("Amount2: %d", amount2);
            console.log("Bitwise AND: %d", amount1 & amount2);
            console.log("Different amounts: %s", amount1 != amount2 ? "true" : "false");
            console.log("Bitwise AND == 0: %s", (amount1 & amount2) == 0 ? "true" : "false");
            
            // This demonstrates the buggy condition
            bool buggyCondition = (amount1 != amount2) && (amount1 & amount2 == 0);
            console.log("Buggy condition would trigger: %s", buggyCondition ? "true" : "false");
            
            if (buggyCondition) {
                console.log("VULNERABILITY: These valid amounts would be rejected by bitwise AND bug");
            }
        }
    }
    
    /**
     * @notice Test the correct vs incorrect logic
     * @dev Shows how the bug manifests and what the correct logic should be
     */
    function testCorrectVsIncorrectLogic() public {
        console.log("=== TESTING CORRECT VS INCORRECT LOGIC ===");
        
        uint256 baseAmount = 1 * 1e18;  // 1 ETH
        uint256 quoteAmount = 2 * 1e18; // 2 ETH equivalent
        
        console.log("Test amounts:");
        console.log("Base amount: %d", baseAmount);
        console.log("Quote amount: %d", quoteAmount);
        
        // Current buggy logic from CLOB.sol
        bool buggyLogic = (baseAmount != quoteAmount) && (baseAmount & quoteAmount == 0);
        console.log("\nBuggy logic (current code):");
        console.log("(baseAmount != quoteAmount): %s", baseAmount != quoteAmount ? "true" : "false");
        console.log("(baseAmount & quoteAmount == 0): %s", (baseAmount & quoteAmount) == 0 ? "true" : "false");
        console.log("Buggy condition result: %s", buggyLogic ? "REVERT" : "PASS");
        
        // Correct logic should check for actual zero amounts
        bool correctLogic = (baseAmount == 0) || (quoteAmount == 0);
        console.log("\nCorrect logic (should be used):");
        console.log("(baseAmount == 0): %s", baseAmount == 0 ? "true" : "false");
        console.log("(quoteAmount == 0): %s", quoteAmount == 0 ? "true" : "false");
        console.log("Correct condition result: %s", correctLogic ? "REVERT" : "PASS");
        
        if (buggyLogic && !correctLogic) {
            console.log("\nVULNERABILITY CONFIRMED:");
            console.log("- Buggy logic would REVERT valid trade");
            console.log("- Correct logic would PASS valid trade");
            console.log("- This demonstrates the bitwise AND bug impact");
        }
    }
    
    /**
     * @notice Test with realistic DeFi amounts that could trigger the bug
     * @dev Uses actual token amounts that could occur in real trading
     */
    function testRealisticDeFiAmounts() public {
        console.log("=== TESTING REALISTIC DEFI AMOUNTS ===");
        
        // Realistic amounts in wei that could trigger the bug
        uint256[] memory realisticAmounts = new uint256[](6);
        realisticAmounts[0] = 1 ether;     // 1 ETH
        realisticAmounts[1] = 2 ether;     // 2 ETH  
        realisticAmounts[2] = 4 ether;     // 4 ETH
        realisticAmounts[3] = 8 ether;     // 8 ETH
        realisticAmounts[4] = 16 ether;    // 16 ETH
        realisticAmounts[5] = 32 ether;    // 32 ETH
        
        console.log("Testing realistic DeFi amounts for bitwise AND bug:");
        
        for (uint i = 0; i < realisticAmounts.length - 1; i++) {
            uint256 amount1 = realisticAmounts[i];
            uint256 amount2 = realisticAmounts[i + 1];
            
            // Check if these amounts would trigger the bug
            bool wouldTriggerBug = (amount1 != amount2) && (amount1 & amount2 == 0);
            
            if (wouldTriggerBug) {
                console.log("\nVULNERABILITY FOUND:");
                console.log("Amount 1: %d ETH", amount1 / 1e18);
                console.log("Amount 2: %d ETH", amount2 / 1e18);
                console.log("These realistic amounts would trigger the bitwise AND bug");
                console.log("Traders using these amounts would face unexpected reverts");
            }
        }
        
        // Test specific power-of-2 relationships that are common in DeFi
        uint256 oneEth = 1 ether;
        uint256 twoEth = 2 ether;
        
        console.log("\nSpecific DeFi scenario:");
        console.log("User trades 1 ETH, receives equivalent of 2 ETH in quote tokens");
        console.log("1 ETH & 2 ETH = %d", oneEth & twoEth);
        
        bool wouldFail = (oneEth != twoEth) && (oneEth & twoEth == 0);
        console.log("Would this realistic trade fail due to bug? %s", wouldFail ? "YES" : "NO");
        
        if (wouldFail) {
            console.log("CRITICAL: Common 1 ETH trades would fail due to bitwise AND bug");
        }
    }
}
