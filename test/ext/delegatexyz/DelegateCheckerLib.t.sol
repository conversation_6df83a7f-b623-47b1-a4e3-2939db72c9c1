// SPDX-License-Identifier: MIT
pragma solidity ^0.8.4;

import "./../../utils/SoladyTest.sol";
import {DelegateCheckerLib} from "../../../src/utils/ext/delegatexyz/DelegateCheckerLib.sol";
import {FixedPointMathLib} from "../../../src/utils/FixedPointMathLib.sol";

interface IDelegateRegistryV1 {
    function delegateForAll(address delegate, bool value) external;
    function delegateForContract(address delegate, address contract_, bool value) external;
    function delegateForToken(address delegate, address contract_, uint256 id, bool value)
        external;
    function checkDelegateForAll(address delegate, address vault) external view returns (bool);
    function checkDelegateForContract(address delegate, address vault, address contract_)
        external
        view
        returns (bool);
    function checkDelegateForToken(address delegate, address vault, address contract_, uint256 id)
        external
        view
        returns (bool);
}

interface IDelegateRegistryV2 {
    function delegateAll(address to, bytes32 rights, bool enable)
        external
        payable
        returns (bytes32 delegationHash);
    function delegateContract(address to, address contract_, bytes32 rights, bool enable)
        external
        payable
        returns (bytes32 delegationHash);
    function delegateERC721(address to, address contract_, uint256 id, bytes32 rights, bool enable)
        external
        payable
        returns (bytes32 delegationHash);
    function delegateERC20(address to, address contract_, bytes32 rights, uint256 amount)
        external
        payable
        returns (bytes32 delegationHash);
    function delegateERC1155(
        address to,
        address contract_,
        uint256 id,
        bytes32 rights,
        uint256 amount
    ) external payable returns (bytes32 delegationHash);
    function checkDelegateForAll(address to, address from, bytes32 rights)
        external
        view
        returns (bool);
    function checkDelegateForContract(address to, address from, address contract_, bytes32 rights)
        external
        view
        returns (bool);
    function checkDelegateForERC721(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) external view returns (bool);
    function checkDelegateForERC20(address to, address from, address contract_, bytes32 rights)
        external
        view
        returns (uint256);
    function checkDelegateForERC1155(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) external view returns (uint256);
}

contract DelegateCheckerLibTest is SoladyTest {
    IDelegateRegistryV1 v1;
    IDelegateRegistryV2 v2;
    bool testForGas;

    function setUp() public {
        vm.etch(
            DelegateCheckerLib.DELEGATE_REGISTRY_V1,
            hex"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"
        );
        vm.etch(
            DelegateCheckerLib.DELEGATE_REGISTRY_V2,
            hex"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"
        );
        v1 = IDelegateRegistryV1(DelegateCheckerLib.DELEGATE_REGISTRY_V1);
        v2 = IDelegateRegistryV2(DelegateCheckerLib.DELEGATE_REGISTRY_V2);
    }

    function _etchStopV1() internal {
        vm.etch(DelegateCheckerLib.DELEGATE_REGISTRY_V1, hex"00");
    }

    function _etchStopV2() internal {
        vm.etch(DelegateCheckerLib.DELEGATE_REGISTRY_V2, hex"00");
    }

    function _randomAmount() internal returns (uint256) {
        if (testForGas) return 111;
        uint256 r = _random();
        if (r & 0x03 == 0) return type(uint256).max;
        if (r & 0x30 == 0) return 0;
        return _random();
    }

    function _maybeDelegateAll(address to, address from, bytes32 rights) internal {
        if (testForGas) return;
        uint256 r = _random();
        if (r & 0x01 == 0) {
            vm.prank(from);
            v1.delegateForAll(to, true);
        }
        if (r & 0x10 == 0) {
            vm.prank(from);
            v2.delegateAll(to, rights, true);
        }
    }

    function _maybeDelegateContract(address to, address from, address contract_, bytes32 rights)
        internal
    {
        if (testForGas) return;
        uint256 r = _random();
        if (r & 0x001 == 0) {
            vm.prank(from);
            v1.delegateForContract(to, contract_, true);
        }
        if (r & 0x010 == 0) {
            vm.prank(from);
            v2.delegateContract(to, contract_, rights, true);
        }
        if (r & 0x100 == 0) _maybeDelegateAll(to, from, rights);
    }

    function _maybeDelegateERC721(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) internal {
        if (testForGas) return;
        uint256 r = _random();
        if (r & 0x001 == 0) {
            vm.prank(from);
            v1.delegateForToken(to, contract_, id, true);
        }
        if (r & 0x010 == 0) {
            vm.prank(from);
            v2.delegateERC721(to, contract_, id, rights, true);
        }
        if (r & 0x100 == 0) {
            _maybeDelegateContract(to, from, contract_, _maybeMutateRights(rights));
        }
    }

    function _maybeDelegateERC20(address to, address from, address contract_, bytes32 rights)
        internal
    {
        if (testForGas) return;
        uint256 r = _random();
        if (r & 0x01 == 0) {
            vm.prank(from);
            v2.delegateERC20(to, contract_, rights, _randomAmount());
        }
        if (r & 0x10 == 0) {
            _maybeDelegateContract(to, from, contract_, _maybeMutateRights(rights));
        }
    }

    function _maybeDelegateERC1155(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) internal {
        if (testForGas) return;
        uint256 r = _random();
        if (r & 0x01 == 0) {
            vm.prank(from);
            v2.delegateERC1155(to, contract_, id, rights, _randomAmount());
        }
        if (r & 0x10 == 0) {
            _maybeDelegateContract(to, from, contract_, _maybeMutateRights(rights));
        }
    }

    function _maybeMutateRights(bytes32 rights) internal returns (bytes32) {
        uint256 r = testForGas ? 0 : _random();
        if (r & 0xf0 == 0) return bytes32(0);
        if (r & 0x07 == 0) return bytes32(_random());
        return rights;
    }

    function _maybeMutateId(uint256 id) internal returns (uint256) {
        uint256 r = testForGas ? 0 : _random();
        if (r & 0x01 == 0) return 0;
        if (r & 0x10 == 0) return _random();
        return id;
    }

    modifier maybeBrutalizeMemory() {
        if (!testForGas) if (_random() & 1 == 0) _brutalizeLowerMemory();
        _;
        if (!testForGas) _checkMemory();
    }

    function _checkDelegateForAll(address to, address from)
        internal
        maybeBrutalizeMemory
        returns (bool)
    {
        return DelegateCheckerLib.checkDelegateForAll(_brutalized(to), _brutalized(from));
    }

    function _checkDelegateForAll(address to, address from, bytes32 rights)
        internal
        maybeBrutalizeMemory
        returns (bool)
    {
        return DelegateCheckerLib.checkDelegateForAll(_brutalized(to), _brutalized(from), rights);
    }

    function _checkDelegateForContract(address to, address from, address contract_)
        internal
        maybeBrutalizeMemory
        returns (bool)
    {
        return DelegateCheckerLib.checkDelegateForContract(
            _brutalized(to), _brutalized(from), _brutalized(contract_)
        );
    }

    function _checkDelegateForContract(address to, address from, address contract_, bytes32 rights)
        internal
        maybeBrutalizeMemory
        returns (bool)
    {
        return DelegateCheckerLib.checkDelegateForContract(
            _brutalized(to), _brutalized(from), _brutalized(contract_), rights
        );
    }

    function _checkDelegateForERC721(address to, address from, address contract_, uint256 id)
        internal
        maybeBrutalizeMemory
        returns (bool)
    {
        return DelegateCheckerLib.checkDelegateForERC721(
            _brutalized(to), _brutalized(from), _brutalized(contract_), id
        );
    }

    function _checkDelegateForERC721(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) internal maybeBrutalizeMemory returns (bool) {
        return DelegateCheckerLib.checkDelegateForERC721(
            _brutalized(to), _brutalized(from), _brutalized(contract_), id, rights
        );
    }

    function _checkDelegateForERC20(address to, address from, address contract_)
        internal
        maybeBrutalizeMemory
        returns (uint256)
    {
        return DelegateCheckerLib.checkDelegateForERC20(
            _brutalized(to), _brutalized(from), _brutalized(contract_)
        );
    }

    function _checkDelegateForERC20(address to, address from, address contract_, bytes32 rights)
        internal
        maybeBrutalizeMemory
        returns (uint256)
    {
        return DelegateCheckerLib.checkDelegateForERC20(
            _brutalized(to), _brutalized(from), _brutalized(contract_), rights
        );
    }

    function _checkDelegateForERC1155(address to, address from, address contract_, uint256 id)
        internal
        maybeBrutalizeMemory
        returns (uint256)
    {
        return DelegateCheckerLib.checkDelegateForERC1155(
            _brutalized(to), _brutalized(from), _brutalized(contract_), id
        );
    }

    function _checkDelegateForERC1155(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) internal maybeBrutalizeMemory returns (uint256) {
        return DelegateCheckerLib.checkDelegateForERC1155(
            _brutalized(to), _brutalized(from), _brutalized(contract_), id, rights
        );
    }

    function testCheckDelegateForAll() public {
        testForGas = true;
        testCheckDelegateForAll(address(111), address(222), bytes32(0));
    }

    function testCheckDelegateForAll(address to, address from, bytes32 rights) public {
        _maybeDelegateAll(to, from, rights);
        rights = _maybeMutateRights(rights);
        assertEq(
            _checkDelegateForAll(to, from),
            v2.checkDelegateForAll(to, from, "") || v1.checkDelegateForAll(to, from)
        );
        assertEq(
            _checkDelegateForAll(to, from, rights),
            v2.checkDelegateForAll(to, from, rights)
                || (rights == "" && v1.checkDelegateForAll(to, from))
        );

        if (testForGas) return;
        if (_random() & 0x3 != 0) return;
        _etchStopV2();
        assertEq(_checkDelegateForAll(to, from), v1.checkDelegateForAll(to, from));
        assertEq(
            _checkDelegateForAll(to, from, rights), rights == "" && v1.checkDelegateForAll(to, from)
        );
        _etchStopV1();
        assertFalse(_checkDelegateForAll(to, from));
        assertFalse(_checkDelegateForAll(to, from, rights));
    }

    function testCheckDelegateForContract() public {
        testForGas = true;
        testCheckDelegateForContract(address(111), address(222), address(333), bytes32(0));
    }

    function testCheckDelegateForContract(
        address to,
        address from,
        address contract_,
        bytes32 rights
    ) public {
        _maybeDelegateContract(to, from, contract_, rights);
        rights = _maybeMutateRights(rights);
        assertEq(
            _checkDelegateForContract(to, from, contract_),
            v2.checkDelegateForContract(to, from, contract_, "")
                || v1.checkDelegateForContract(to, from, contract_)
        );
        if (_checkDelegateForAll(to, from)) {
            assertTrue(_checkDelegateForContract(to, from, contract_));
        }
        assertEq(
            _checkDelegateForContract(to, from, contract_, rights),
            v2.checkDelegateForContract(to, from, contract_, rights)
                || (rights == "" && v1.checkDelegateForContract(to, from, contract_))
        );
        if (_checkDelegateForAll(to, from, rights)) {
            assertTrue(_checkDelegateForContract(to, from, contract_, rights));
        }

        if (testForGas) return;
        if (_random() & 0x3 != 0) return;
        _etchStopV2();
        assertEq(
            _checkDelegateForContract(to, from, contract_),
            v1.checkDelegateForContract(to, from, contract_)
        );
        assertEq(
            _checkDelegateForContract(to, from, contract_, rights),
            rights == "" && v1.checkDelegateForContract(to, from, contract_)
        );
        _etchStopV1();
        assertFalse(_checkDelegateForContract(to, from, contract_));
        assertFalse(_checkDelegateForContract(to, from, contract_, rights));
    }

    function testCheckDelegateForERC721() public {
        testForGas = true;
        testCheckDelegateForERC721(address(111), address(222), address(333), 1, bytes32(0));
    }

    function testCheckDelegateForERC721(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) public {
        _maybeDelegateERC721(to, from, contract_, id, rights);
        rights = _maybeMutateRights(rights);
        id = _maybeMutateId(id);
        assertEq(
            _checkDelegateForERC721(to, from, contract_, id),
            v2.checkDelegateForERC721(to, from, contract_, id, "")
                || v1.checkDelegateForToken(to, from, contract_, id)
        );
        if (_checkDelegateForContract(to, from, contract_)) {
            assertTrue(_checkDelegateForERC721(to, from, contract_, id));
        }
        assertEq(
            _checkDelegateForERC721(to, from, contract_, id, rights),
            v2.checkDelegateForERC721(to, from, contract_, id, rights)
                || (rights == "" && v1.checkDelegateForToken(to, from, contract_, id))
        );
        if (_checkDelegateForContract(to, from, contract_, rights)) {
            assertTrue(_checkDelegateForERC721(to, from, contract_, id, rights));
        }

        if (testForGas) return;
        if (_random() & 0x3 != 0) return;
        _etchStopV2();
        assertEq(
            _checkDelegateForERC721(to, from, contract_, id),
            v1.checkDelegateForToken(to, from, contract_, id)
        );
        assertEq(
            _checkDelegateForERC721(to, from, contract_, id, rights),
            rights == "" && v1.checkDelegateForToken(to, from, contract_, id)
        );
        _etchStopV1();
        assertFalse(_checkDelegateForERC721(to, from, contract_, id));
        assertFalse(_checkDelegateForERC721(to, from, contract_, id, rights));
    }

    function testCheckDelegateForERC20() public {
        testForGas = true;
        testCheckDelegateForERC20(address(111), address(222), address(333), bytes32(0));
    }

    function testCheckDelegateForERC20(address to, address from, address contract_, bytes32 rights)
        public
    {
        _maybeDelegateERC20(to, from, contract_, rights);
        rights = _maybeMutateRights(rights);
        assertEq(
            _checkDelegateForERC20(to, from, contract_),
            FixedPointMathLib.max(
                v2.checkDelegateForERC20(to, from, contract_, ""),
                _uintMaxIfTrueElse0(v1.checkDelegateForContract(to, from, contract_))
            )
        );
        if (_checkDelegateForContract(to, from, contract_)) {
            assertEq(_checkDelegateForERC20(to, from, contract_), type(uint256).max);
        }
        assertEq(
            _checkDelegateForERC20(to, from, contract_, rights),
            FixedPointMathLib.max(
                v2.checkDelegateForERC20(to, from, contract_, rights),
                _uintMaxIfTrueElse0(
                    rights == "" && v1.checkDelegateForContract(to, from, contract_)
                )
            )
        );
        if (_checkDelegateForContract(to, from, contract_, rights)) {
            assertEq(_checkDelegateForERC20(to, from, contract_, rights), type(uint256).max);
        }

        if (testForGas) return;
        if (_random() & 0x3 != 0) return;
        _etchStopV2();
        assertEq(
            _checkDelegateForERC20(to, from, contract_),
            _uintMaxIfTrueElse0(v1.checkDelegateForContract(to, from, contract_))
        );
        assertEq(
            _checkDelegateForERC20(to, from, contract_, rights),
            _uintMaxIfTrueElse0(rights == "" && v1.checkDelegateForContract(to, from, contract_))
        );
        _etchStopV1();
        assertEq(_checkDelegateForERC20(to, from, contract_), 0);
        assertEq(_checkDelegateForERC20(to, from, contract_, rights), 0);
    }

    function testCheckDelegateForERC1155() public {
        testForGas = true;
        testCheckDelegateForERC1155(address(111), address(222), address(333), 11, bytes32(0));
    }

    function testCheckDelegateForERC1155(
        address to,
        address from,
        address contract_,
        uint256 id,
        bytes32 rights
    ) public {
        _maybeDelegateERC1155(to, from, contract_, id, rights);
        rights = _maybeMutateRights(rights);
        id = _maybeMutateId(id);
        assertEq(
            _checkDelegateForERC1155(to, from, contract_, id),
            FixedPointMathLib.max(
                v2.checkDelegateForERC1155(to, from, contract_, id, ""),
                _uintMaxIfTrueElse0(v1.checkDelegateForContract(to, from, contract_))
            )
        );
        if (_checkDelegateForContract(to, from, contract_)) {
            assertEq(_checkDelegateForERC1155(to, from, contract_, id), type(uint256).max);
        }
        assertEq(
            _checkDelegateForERC1155(to, from, contract_, id, rights),
            FixedPointMathLib.max(
                v2.checkDelegateForERC1155(to, from, contract_, id, rights),
                _uintMaxIfTrueElse0(
                    rights == "" && v1.checkDelegateForContract(to, from, contract_)
                )
            )
        );
        if (_checkDelegateForContract(to, from, contract_, rights)) {
            assertEq(_checkDelegateForERC1155(to, from, contract_, id, rights), type(uint256).max);
        }

        if (testForGas) return;
        if (_random() & 0x3 != 0) return;
        _etchStopV2();
        assertEq(
            _checkDelegateForERC1155(to, from, contract_, id),
            _uintMaxIfTrueElse0(v1.checkDelegateForContract(to, from, contract_))
        );
        assertEq(
            _checkDelegateForERC1155(to, from, contract_, id, rights),
            _uintMaxIfTrueElse0(rights == "" && v1.checkDelegateForContract(to, from, contract_))
        );
        _etchStopV1();
        assertEq(_checkDelegateForERC1155(to, from, contract_, id), 0);
        assertEq(_checkDelegateForERC1155(to, from, contract_, id, rights), 0);
    }

    function _uintMaxIfTrueElse0(bool b) internal pure returns (uint256) {
        return b ? type(uint256).max : 0;
    }
}
