// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {PoCTestBase} from "./PoCTestBase.t.sol";
import {CLOBManager, SettingsParams} from "contracts/clob/CLOBManager.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {Side} from "contracts/clob/types/Order.sol";
import "forge-std/console.sol";

/**
 * Test to verify the missing validation in market creation
 * Tests if minLimitOrderAmountInBase can be set to a value that's not a multiple of lotSizeInBase
 */
contract TestMarketCreationValidation is PoCTestBase {

    function test_wMissingLotSizeValidationInMarketCreation() external {
        console.log("=== Testing Missing Lot Size Validation in Market Creation ===");

        // Step 1: Create market settings with incompatible values
        // minLimitOrderAmountInBase = 1.5 ETH, lotSizeInBase = 1 ETH
        // 1.5 ETH % 1 ETH = 0.5 ETH != 0 (not a multiple)

        SettingsParams memory brokenSettings = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 1.5 ether, // 1.5 ETH minimum
            tickSize: 0.0001 ether,
            lotSizeInBase: 1 ether // 1 ETH lot size
        });

        console.log("Attempting to create market with incompatible settings:");
        console.log("- minLimitOrderAmountInBase:", brokenSettings.minLimitOrderAmountInBase);
        console.log("- lotSizeInBase:", brokenSettings.lotSizeInBase);
        console.log("- 1.5 ETH % 1 ETH =", brokenSettings.minLimitOrderAmountInBase % brokenSettings.lotSizeInBase);

        // Step 2: Try to create the market (should succeed but create broken market)
        address brokenMarket;
        try clobManager.createMarket(address(tokenA), address(USDC), brokenSettings) returns (address market) {
            brokenMarket = market;
            console.log("[VULNERABILITY CONFIRMED] Market creation succeeded with incompatible settings!");
            console.log("Broken market address:", brokenMarket);
        } catch {
            console.log("[NOT CONFIRMED] Market creation failed - system has validation");
            return;
        }

        // Step 3: Setup user with funds
        address testUser = makeAddr("testUser");
        vm.startPrank(testUser);

        // Mint and deposit tokens
        uint256 depositAmount = 10 ether; // 10 ETH
        tokenA.mint(testUser, depositAmount);
        tokenA.approve(address(accountManager), depositAmount);
        accountManager.deposit(testUser, address(tokenA), depositAmount);

        console.log("User deposited", depositAmount / 1e18, "ETH");

        // Step 4: Try to place minimum order (should fail due to lot size incompatibility)
        ICLOB.PostLimitOrderArgs memory minOrderArgs = ICLOB.PostLimitOrderArgs({
            clientOrderId: 1,
            amountInBase: brokenSettings.minLimitOrderAmountInBase, // 1.5 ETH
            price: 2000 ether, // $2000 per ETH
            cancelTimestamp: uint32(block.timestamp + 1 days),
            side: Side.SELL,
            limitOrderType: ICLOB.LimitOrderType.POST_ONLY
        });

        console.log("Attempting to place minimum order of 1.5 ETH...");

        try ICLOB(brokenMarket).postLimitOrder(testUser, minOrderArgs) {
            console.log("[ERROR] Minimum order succeeded - this should not happen!");
        } catch {
            console.log("[CONFIRMED] Minimum order failed due to lot size incompatibility!");
            console.log("   - Market accepts 1.5 ETH minimum but requires 1 ETH multiples");
            console.log("   - 1.5 ETH is not a multiple of 1 ETH");
            console.log("   - Users cannot place valid orders at minimum size");
            console.log("   - Market is effectively broken");
        }

        // Step 5: Try to place a lot-size compliant order (should work)
        ICLOB.PostLimitOrderArgs memory validOrderArgs = ICLOB.PostLimitOrderArgs({
            clientOrderId: 2,
            amountInBase: 2 ether, // 2 ETH (multiple of 1 ETH)
            price: 2000 ether,
            cancelTimestamp: uint32(block.timestamp + 1 days),
            side: Side.SELL,
            limitOrderType: ICLOB.LimitOrderType.POST_ONLY
        });

        console.log("Attempting to place lot-size compliant order of 2 ETH...");

        try ICLOB(brokenMarket).postLimitOrder(testUser, validOrderArgs) {
            console.log("[CONFIRMED] Lot-size compliant order succeeded");
            console.log("   - 2 ETH is a multiple of 1 ETH lot size");
            console.log("   - But users still cannot use the advertised minimum");
        } catch {
            console.log("[UNEXPECTED] Even lot-size compliant order failed");
        }

        vm.stopPrank();
    }

    function test_RecommendedFix() external {
        console.log("=== Testing Recommended Fix ===");

        // This test demonstrates what the fix should do
        SettingsParams memory invalidSettings = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 1.7 ether, // Not a multiple of 0.5
            tickSize: 0.0001 ether,
            lotSizeInBase: 0.5 ether
        });

        console.log("Testing fix validation logic:");
        console.log("minLimitOrderAmountInBase:", invalidSettings.minLimitOrderAmountInBase);
        console.log("lotSizeInBase:", invalidSettings.lotSizeInBase);

        // Simulate the recommended validation
        bool shouldRevert = invalidSettings.minLimitOrderAmountInBase % invalidSettings.lotSizeInBase != 0;
        console.log("Should revert with fix:", shouldRevert);
        console.log("Remainder:", invalidSettings.minLimitOrderAmountInBase % invalidSettings.lotSizeInBase);

        if (shouldRevert) {
            console.log("[RECOMMENDED FIX] This combination should be rejected");
            console.log("Add to _assertValidSettings:");
            console.log("if (settings.minLimitOrderAmountInBase % settings.lotSizeInBase != 0) {");
            console.log("    revert InvalidSettings();");
            console.log("}");
        }
    }
}