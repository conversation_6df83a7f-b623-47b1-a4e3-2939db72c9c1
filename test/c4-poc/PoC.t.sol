// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {PoCTestBase} from "./PoCTestBase.t.sol";
import "forge-std/console.sol";

/**
 * Test to verify the bitwise AND bug in CLOB order validation
 * This tests the incorrect use of bitwise AND instead of proper zero checks
 */
contract TestBitwiseAndBug is PoCTestBase {

    // Contract to test the buggy logic
    BitwiseAndTester tester;

    function setUp() public override {
        super.setUp();
        tester = new BitwiseAndTester();
    }

    function test_BitwiseAndBugInOrderValidation() external {
        console.log("=== Testing Bitwise AND Bug in Order Validation ===");

        // Test the specific vulnerable case: 1 & 16 = 0
        uint256 baseTokenAmountReceived = 1;
        uint256 quoteTokenAmountSent = 16;

        console.log("Testing scenario: baseTokenAmountReceived=", baseTokenAmountReceived, "quoteTokenAmountSent=", quoteTokenAmountSent);
        console.log("Expected: Trade should be valid (different amounts, both non-zero)");
        console.log("");

        // Show the bitwise AND result
        uint256 bitwiseResult = baseTokenAmountReceived & quoteTokenAmountSent;
        console.log("Bitwise AND result:", baseTokenAmountReceived, "&", quoteTokenAmountSent, "=", bitwiseResult);
        console.log("(non-zero amounts but zero bitwise result)");

        // Test current buggy logic
        bool buggyLogic = (baseTokenAmountReceived != quoteTokenAmountSent) &&
                         (baseTokenAmountReceived & quoteTokenAmountSent == 0);

        // Test correct logic
        bool correctLogic = (baseTokenAmountReceived == 0) || (quoteTokenAmountSent == 0);

        console.log("Current buggy logic result:", buggyLogic ? "true (INCORRECTLY REJECTS)" : "false");
        console.log("Correct logic result:", correctLogic ? "true" : "false (CORRECTLY ALLOWS)");

        if (buggyLogic && !correctLogic) {
            console.log("");
            console.log("[VULNERABILITY] CONFIRMED: Bitwise AND logic causes incorrect rejections!");
        }

        // Test the actual functions
        console.log("");
        console.log("Testing with contract functions:");
        
        bool shouldRevertBuggy = tester.testBuggyZeroCostCheck(baseTokenAmountReceived, quoteTokenAmountSent);
        bool shouldRevertCorrect = tester.testCorrectZeroCostCheck(baseTokenAmountReceived, quoteTokenAmountSent);

        console.log("Buggy function says should revert:", shouldRevertBuggy ? "true" : "false");
        console.log("Correct function says should revert:", shouldRevertCorrect ? "true" : "false");

        // Test multiple vulnerable combinations
        console.log("");
        console.log("Testing vulnerable combinations:");
        
        uint256 vulnerableCount = 0;
        uint256[10] memory testAmounts = [uint256(1), 2, 4, 8, 16, 32, 64, 128, 256, 512];
        
        for (uint i = 0; i < testAmounts.length; i++) {
            for (uint j = i + 1; j < testAmounts.length; j++) {
                uint256 amount1 = testAmounts[i];
                uint256 amount2 = testAmounts[j];
                
                // Check if this combination is vulnerable
                bool isVulnerable = (amount1 != amount2) && (amount1 & amount2 == 0);
                bool shouldBeValid = (amount1 != 0) && (amount2 != 0);
                
                if (isVulnerable && shouldBeValid) {
                    console.log("VULNERABLE:", amount1, "&", amount2, "=", amount1 & amount2, "(both non-zero but bitwise AND is zero)");
                    vulnerableCount++;
                }
            }
        }
        
        console.log("");
        console.log("Total vulnerable combinations found:", vulnerableCount);
    }

    function test_ZeroCostTradeDetection() external {
        console.log("=== Testing Zero-Cost Trade Detection ===");

        // Test actual zero-cost scenarios (should be rejected)
        console.log("Testing legitimate zero-cost trades (should be rejected):");
        
        bool shouldReject1 = tester.testCorrectZeroCostCheck(0, 100);
        bool shouldReject2 = tester.testCorrectZeroCostCheck(100, 0);
        bool shouldReject3 = tester.testCorrectZeroCostCheck(0, 0);
        
        console.log("Amount1=0, Amount2=100: Should reject =", shouldReject1 ? "true" : "false");
        console.log("Amount1=100, Amount2=0: Should reject =", shouldReject2 ? "true" : "false");
        console.log("Amount1=0, Amount2=0: Should reject =", shouldReject3 ? "true" : "false");

        // Test valid trades (should be allowed)
        console.log("");
        console.log("Testing valid trades (should be allowed):");
        
        bool shouldReject4 = tester.testCorrectZeroCostCheck(100, 200);
        bool shouldReject5 = tester.testCorrectZeroCostCheck(1000, 2000);
        
        console.log("Amount1=100, Amount2=200: Should reject =", shouldReject4 ? "true" : "false");
        console.log("Amount1=1000, Amount2=2000: Should reject =", shouldReject5 ? "true" : "false");
    }

    function test_BinaryAnalysis() external {
        console.log("=== Binary Analysis of Vulnerable Combinations ===");

        uint256 amount1 = 1;   // Binary: 0001
        uint256 amount2 = 16;  // Binary: 10000

        console.log("Amount 1:", amount1, "(binary: 0001)");
        console.log("Amount 2:", amount2, "(binary: 10000)");
        console.log("Bitwise AND:", amount1 & amount2, "(binary: 00000)");
        console.log("");
        console.log("Both amounts are non-zero, but bitwise AND is zero");
        console.log("This causes the buggy logic to incorrectly reject the trade");
    }
}

// Helper contract to test the buggy and correct logic
contract BitwiseAndTester {
    
    // Simulates the current buggy logic from CLOB contract
    function testBuggyZeroCostCheck(uint256 baseAmount, uint256 quoteAmount) 
        external 
        pure 
        returns (bool shouldRevert) 
    {
        // This is the buggy logic from lines 503-504 and 544-545
        return (baseAmount != quoteAmount) && (baseAmount & quoteAmount == 0);
    }
    
    // Simulates the correct logic that should be used
    function testCorrectZeroCostCheck(uint256 baseAmount, uint256 quoteAmount) 
        external 
        pure 
        returns (bool shouldRevert) 
    {
        // This is the correct logic - reject only if either amount is actually zero
        return (baseAmount == 0) || (quoteAmount == 0);
    }
    
    // Function to demonstrate the difference
    function compareBuggyVsCorrect(uint256 baseAmount, uint256 quoteAmount)
        external
        pure
        returns (bool buggyResult, bool correctResult, bool hasBug)
    {
        buggyResult = this.testBuggyZeroCostCheck(baseAmount, quoteAmount);
        correctResult = this.testCorrectZeroCostCheck(baseAmount, quoteAmount);
        hasBug = (buggyResult != correctResult);
    }
}
