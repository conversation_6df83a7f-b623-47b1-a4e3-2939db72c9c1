// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {PoCTestBase} from "./PoCTestBase.t.sol";
import "forge-std/Test.sol";

/**
 * Test to verify the bitwise AND bug in CLOB order validation
 * This tests the incorrect use of bitwise AND instead of proper zero checks
 */
contract TestBitwiseAndBug is PoCTestBase {

    // Contract to test the buggy logic
    BitwiseAndTester tester;

    function setUp() public override {
        super.setUp();
        tester = new BitwiseAndTester();
    }

    function test_BitwiseAndBugInOrderValidation() external {
        // Test the specific vulnerable case: 1 & 16 = 0
        uint256 baseTokenAmountReceived = 1;
        uint256 quoteTokenAmountSent = 16;

        // Show the bitwise AND result
        uint256 bitwiseResult = baseTokenAmountReceived & quoteTokenAmountSent;
        assertEq(bitwiseResult, 0, "Bitwise AND should be 0 for 1 & 16");
        
        // Test current buggy logic
        bool buggyLogic = (baseTokenAmountReceived != quoteTokenAmountSent) && 
                         (baseTokenAmountReceived & quoteTokenAmountSent == 0);
        
        // Test correct logic
        bool correctLogic = (baseTokenAmountReceived == 0) || (quoteTokenAmountSent == 0);

        // Verify the vulnerability exists
        assertTrue(buggyLogic, "Buggy logic should incorrectly reject valid trade");
        assertFalse(correctLogic, "Correct logic should allow valid trade");
        
        // Test the actual functions
        bool shouldRevertBuggy = tester.testBuggyZeroCostCheck(baseTokenAmountReceived, quoteTokenAmountSent);
        bool shouldRevertCorrect = tester.testCorrectZeroCostCheck(baseTokenAmountReceived, quoteTokenAmountSent);

        assertTrue(shouldRevertBuggy, "Buggy function incorrectly says should revert");
        assertFalse(shouldRevertCorrect, "Correct function correctly says should not revert");

        // Test multiple vulnerable combinations
        uint256 vulnerableCount = 0;
        uint256[10] memory testAmounts = [uint256(1), 2, 4, 8, 16, 32, 64, 128, 256, 512];
        
        for (uint i = 0; i < testAmounts.length; i++) {
            for (uint j = i + 1; j < testAmounts.length; j++) {
                uint256 amount1 = testAmounts[i];
                uint256 amount2 = testAmounts[j];
                
                // Check if this combination is vulnerable
                bool isVulnerable = (amount1 != amount2) && (amount1 & amount2 == 0);
                bool shouldBeValid = (amount1 != 0) && (amount2 != 0);
                
                if (isVulnerable && shouldBeValid) {
                    vulnerableCount++;
                }
            }
        }
        
        // Verify we found vulnerable combinations
        assertGt(vulnerableCount, 0, "Should find vulnerable combinations");
        assertEq(vulnerableCount, 45, "Should find exactly 45 vulnerable combinations");
    }

    function test_ZeroCostTradeDetection() external {
        // Test actual zero-cost scenarios (should be rejected)
        bool shouldReject1 = tester.testCorrectZeroCostCheck(0, 100);
        bool shouldReject2 = tester.testCorrectZeroCostCheck(100, 0);
        bool shouldReject3 = tester.testCorrectZeroCostCheck(0, 0);
        
        assertTrue(shouldReject1, "Should reject when base amount is 0");
        assertTrue(shouldReject2, "Should reject when quote amount is 0");
        assertTrue(shouldReject3, "Should reject when both amounts are 0");

        // Test valid trades (should be allowed)
        bool shouldReject4 = tester.testCorrectZeroCostCheck(100, 200);
        bool shouldReject5 = tester.testCorrectZeroCostCheck(1000, 2000);
        
        assertFalse(shouldReject4, "Should allow valid trade with different amounts");
        assertFalse(shouldReject5, "Should allow valid trade with different amounts");
    }

    function test_BinaryAnalysis() external {
        uint256 amount1 = 1;   // Binary: 0001
        uint256 amount2 = 16;  // Binary: 10000

        // Verify the binary analysis
        assertEq(amount1 & amount2, 0, "1 & 16 should equal 0 (non-overlapping bits)");
        
        // Both amounts are non-zero, but bitwise AND is zero
        assertTrue(amount1 != 0, "Amount1 should be non-zero");
        assertTrue(amount2 != 0, "Amount2 should be non-zero");
        assertTrue((amount1 & amount2) == 0, "Bitwise AND should be zero");
        
        // This causes the buggy logic to incorrectly reject the trade
        bool buggyRejects = (amount1 != amount2) && (amount1 & amount2 == 0);
        bool correctRejects = (amount1 == 0) || (amount2 == 0);
        
        assertTrue(buggyRejects, "Buggy logic incorrectly rejects valid trade");
        assertFalse(correctRejects, "Correct logic correctly allows valid trade");
    }

    function test_ComprehensiveVulnerabilityCheck() external {
        // Test all power-of-2 combinations up to 1024
        uint256[] memory powers = new uint256[](11);
        powers[0] = 1;
        powers[1] = 2;
        powers[2] = 4;
        powers[3] = 8;
        powers[4] = 16;
        powers[5] = 32;
        powers[6] = 64;
        powers[7] = 128;
        powers[8] = 256;
        powers[9] = 512;
        powers[10] = 1024;
        
        uint256 totalVulnerable = 0;
        
        for (uint i = 0; i < powers.length; i++) {
            for (uint j = i + 1; j < powers.length; j++) {
                uint256 a = powers[i];
                uint256 b = powers[j];
                
                // Powers of 2 with different exponents have non-overlapping bits
                if ((a & b) == 0) {
                    totalVulnerable++;
                    
                    // Verify this is indeed vulnerable
                    bool buggyLogic = (a != b) && (a & b == 0);
                    bool correctLogic = (a == 0) || (b == 0);
                    
                    assertTrue(buggyLogic, "Should be vulnerable to buggy logic");
                    assertFalse(correctLogic, "Should not be rejected by correct logic");
                }
            }
        }
        
        // All power-of-2 pairs should be vulnerable
        assertEq(totalVulnerable, 55, "Should find 55 vulnerable power-of-2 combinations");
    }
}

// Helper contract to test the buggy and correct logic
contract BitwiseAndTester {
    
    // Simulates the current buggy logic from CLOB contract
    function testBuggyZeroCostCheck(uint256 baseAmount, uint256 quoteAmount) 
        external 
        pure 
        returns (bool shouldRevert) 
    {
        // This is the buggy logic from lines 503-504 and 544-545
        return (baseAmount != quoteAmount) && (baseAmount & quoteAmount == 0);
    }
    
    // Simulates the correct logic that should be used
    function testCorrectZeroCostCheck(uint256 baseAmount, uint256 quoteAmount) 
        external 
        pure 
        returns (bool shouldRevert) 
    {
        // This is the correct logic - reject only if either amount is actually zero
        return (baseAmount == 0) || (quoteAmount == 0);
    }
    
    // Function to demonstrate the difference
    function compareBuggyVsCorrect(uint256 baseAmount, uint256 quoteAmount)
        external
        view
        returns (bool buggyResult, bool correctResult, bool hasBug)
    {
        buggyResult = this.testBuggyZeroCostCheck(baseAmount, quoteAmount);
        correctResult = this.testCorrectZeroCostCheck(baseAmount, quoteAmount);
        hasBug = (buggyResult != correctResult);
    }
}
