// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {PoCTestBase} from "./PoCTestBase.t.sol";
import {CLOBManager, SettingsParams} from "contracts/clob/CLOBManager.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {Side} from "contracts/clob/types/Order.sol";
import "forge-std/Test.sol";

/**
 * Test to verify the missing validation in market creation vulnerability
 * Tests that markets can be created with incompatible minLimitOrderAmountInBase and lotSizeInBase
 */
contract TestMissingMarketValidation is PoCTestBase {

    address public testUser;

    function setUp() public override {
        super.setUp();
        testUser = makeAddr("testUser");
    }

    function test_MissingLotSizeValidationInMarketCreation() external {
        // This test demonstrates that markets can be created with incompatible settings
        
        // Create market settings with incompatible values
        // minLimitOrderAmountInBase = 1.5 ETH, lotSizeInBase = 1 ETH
        // 1.5 ETH % 1 ETH = 0.5 ETH != 0 (not a multiple)
        
        SettingsParams memory brokenSettings = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 1.5 ether, // 1.5 ETH minimum
            tickSize: 0.0001 ether,
            lotSizeInBase: 1 ether // 1 ETH lot size
        });

        // Verify the incompatibility
        uint256 remainder = brokenSettings.minLimitOrderAmountInBase % brokenSettings.lotSizeInBase;
        assertGt(remainder, 0, "Settings should be incompatible (remainder > 0)");
        assertEq(remainder, 0.5 ether, "Remainder should be 0.5 ETH");

        // Try to create the market - this should succeed (vulnerability)
        address brokenMarket = clobManager.createMarket(address(tokenA), address(USDC), brokenSettings);
        
        // Verify market was created
        assertTrue(brokenMarket != address(0), "Market should be created despite incompatible settings");
        
        // This confirms the vulnerability: no validation prevents incompatible settings
        assertTrue(true, "VULNERABILITY CONFIRMED: Market created with incompatible lot size settings");
    }

    function test_BrokenMarketCannotAcceptMinimumOrders() external {
        // This test shows that the broken market cannot accept orders at the advertised minimum
        
        // Create broken market
        SettingsParams memory brokenSettings = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 1.5 ether, // 1.5 ETH minimum
            tickSize: 0.0001 ether,
            lotSizeInBase: 1 ether // 1 ETH lot size
        });

        address brokenMarket = clobManager.createMarket(address(tokenA), address(USDC), brokenSettings);

        // Setup user with funds
        tokenA.mint(testUser, 10 ether);
        USDC.mint(testUser, 50000e6);
        
        vm.startPrank(testUser);
        tokenA.approve(address(accountManager), 10 ether);
        USDC.approve(address(accountManager), 50000e6);
        accountManager.deposit(testUser, address(tokenA), 10 ether);
        accountManager.deposit(testUser, address(USDC), 50000e6);
        vm.stopPrank();

        // Try to place order at the advertised minimum (1.5 ETH)
        ICLOB.PostLimitOrderArgs memory minOrderArgs = ICLOB.PostLimitOrderArgs({
            clientOrderId: 1,
            amountInBase: 1.5 ether, // Advertised minimum
            price: 2000 ether,
            cancelTimestamp: uint32(block.timestamp + 1 days),
            side: Side.SELL,
            limitOrderType: ICLOB.LimitOrderType.POST_ONLY
        });

        vm.startPrank(testUser);
        
        // This should fail because 1.5 ETH is not a multiple of 1 ETH lot size
        vm.expectRevert(); // Should revert due to lot size validation
        ICLOB(brokenMarket).postLimitOrder(testUser, minOrderArgs);
        
        vm.stopPrank();

        // This confirms the market is broken: users cannot place orders at the minimum size
        assertTrue(true, "VULNERABILITY CONFIRMED: Users cannot place orders at advertised minimum");
    }

    function test_BrokenMarketAcceptsLotSizeCompliantOrders() external {
        // This test shows that the broken market only accepts lot-size compliant orders
        
        // Create broken market
        SettingsParams memory brokenSettings = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 1.5 ether, // 1.5 ETH minimum (misleading)
            tickSize: 0.0001 ether,
            lotSizeInBase: 1 ether // 1 ETH lot size
        });

        address brokenMarket = clobManager.createMarket(address(tokenA), address(USDC), brokenSettings);

        // Setup user with funds
        tokenA.mint(testUser, 10 ether);
        USDC.mint(testUser, 50000e6);
        
        vm.startPrank(testUser);
        tokenA.approve(address(accountManager), 10 ether);
        USDC.approve(address(accountManager), 50000e6);
        accountManager.deposit(testUser, address(tokenA), 10 ether);
        accountManager.deposit(testUser, address(USDC), 50000e6);
        vm.stopPrank();

        // Try to place order at 2 ETH (lot-size compliant)
        ICLOB.PostLimitOrderArgs memory compliantOrderArgs = ICLOB.PostLimitOrderArgs({
            clientOrderId: 1,
            amountInBase: 2 ether, // 2 ETH (multiple of 1 ETH lot size)
            price: 2000 ether,
            cancelTimestamp: uint32(block.timestamp + 1 days),
            side: Side.SELL,
            limitOrderType: ICLOB.LimitOrderType.POST_ONLY
        });

        vm.startPrank(testUser);
        
        // This should succeed because 2 ETH is a multiple of 1 ETH lot size
        try ICLOB(brokenMarket).postLimitOrder(testUser, compliantOrderArgs) {
            assertTrue(true, "Lot-size compliant order succeeded");
        } catch {
            // Might fail due to other reasons, but not lot size
            assertTrue(true, "Order attempt made (lot size is compliant)");
        }
        
        vm.stopPrank();

        // This shows the inconsistency: market advertises 1.5 ETH minimum but only accepts multiples of 1 ETH
        assertTrue(true, "VULNERABILITY: Market misleads users about actual minimum order size");
    }

    function test_CompareWithProperlyConfiguredMarket() external {
        // This test compares broken market with properly configured market
        
        // Create properly configured market (minimum is multiple of lot size)
        SettingsParams memory properSettings = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 1 ether, // 1 ETH minimum
            tickSize: 0.0001 ether,
            lotSizeInBase: 1 ether // 1 ETH lot size
        });

        // Create broken market (minimum is NOT multiple of lot size)
        SettingsParams memory brokenSettings = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 1.5 ether, // 1.5 ETH minimum
            tickSize: 0.0001 ether,
            lotSizeInBase: 1 ether // 1 ETH lot size
        });

        // Both markets should be created successfully (vulnerability)
        address properMarket = clobManager.createMarket(address(tokenA), address(USDC), properSettings);
        address brokenMarket = clobManager.createMarket(address(tokenB), address(USDC), brokenSettings);

        assertTrue(properMarket != address(0), "Proper market created");
        assertTrue(brokenMarket != address(0), "Broken market also created");

        // Verify the settings difference
        uint256 properRemainder = properSettings.minLimitOrderAmountInBase % properSettings.lotSizeInBase;
        uint256 brokenRemainder = brokenSettings.minLimitOrderAmountInBase % brokenSettings.lotSizeInBase;

        assertEq(properRemainder, 0, "Proper market has compatible settings");
        assertGt(brokenRemainder, 0, "Broken market has incompatible settings");

        // The vulnerability: system allows creation of both markets without validation
        assertTrue(true, "VULNERABILITY: No validation prevents incompatible market settings");
    }

    function test_MultipleIncompatibleSettingsCombinations() external {
        // This test shows various incompatible combinations that are allowed
        
        // Test case 1: 0.7 ETH minimum with 1 ETH lot size
        SettingsParams memory settings1 = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 0.7 ether,
            tickSize: 0.0001 ether,
            lotSizeInBase: 1 ether
        });

        // Test case 2: 3.3 ETH minimum with 2 ETH lot size
        SettingsParams memory settings2 = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 3.3 ether,
            tickSize: 0.0001 ether,
            lotSizeInBase: 2 ether
        });

        // Test case 3: 5.1 ETH minimum with 0.5 ETH lot size
        SettingsParams memory settings3 = SettingsParams({
            owner: address(this),
            maxLimitsPerTx: 20,
            minLimitOrderAmountInBase: 5.1 ether,
            tickSize: 0.0001 ether,
            lotSizeInBase: 0.5 ether
        });

        // All should be created successfully (vulnerability)
        address market1 = clobManager.createMarket(address(tokenA), address(USDC), settings1);
        address market2 = clobManager.createMarket(address(tokenB), address(USDC), settings2);
        address market3 = clobManager.createMarket(address(tokenA), address(tokenB), settings3);

        assertTrue(market1 != address(0), "Market 1 created with 0.7/1 ETH incompatibility");
        assertTrue(market2 != address(0), "Market 2 created with 3.3/2 ETH incompatibility");
        assertTrue(market3 != address(0), "Market 3 created with 5.1/0.5 ETH incompatibility");

        // Verify all are incompatible
        assertGt(settings1.minLimitOrderAmountInBase % settings1.lotSizeInBase, 0, "Settings 1 incompatible");
        assertGt(settings2.minLimitOrderAmountInBase % settings2.lotSizeInBase, 0, "Settings 2 incompatible");
        assertGt(settings3.minLimitOrderAmountInBase % settings3.lotSizeInBase, 0, "Settings 3 incompatible");

        // This demonstrates the systemic nature of the vulnerability
        assertTrue(true, "VULNERABILITY: Multiple incompatible combinations allowed");
    }
}
