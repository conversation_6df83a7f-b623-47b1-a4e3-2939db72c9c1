// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.27;

import {Test, console} from "forge-std/Test.sol";
import {CLOBTestBase} from "./clob/utils/CLOBTestBase.sol";
import {ICLOB} from "contracts/clob/ICLOB.sol";
import {Side} from "contracts/clob/types/Order.sol";
import {ERC20Harness} from "./harnesses/ERC20Harness.sol";

contract PrecisionLossTest is CLOBTestBase {

    function setUp() public override {
        super.setUp();
        // Create a new market with different tokens to avoid MarketExists error
        baseToken = new ERC20Harness("Test Base", "TB");
        quoteToken = new ERC20Harness("Test Quote", "TQ");
        deployClob();
    }

    // Test precision loss in CLOB calculation functions
    function testPrecisionLossInCalculations() public view {
        // Test with different price ranges to find precision loss
        uint256[5] memory testPrices = [
            TICK_SIZE,           // Minimum tick size
            TICK_SIZE * 10,      // 10x tick size
            1e18,                // 1 ether
            1e21,                // 1000 ether (large price)
            1e24                 // 1,000,000 ether (very large price)
        ];

        for (uint256 i = 0; i < testPrices.length; i++) {
            uint256 price = testPrices[i];
            console.log("Testing price:", price);

            // Test with very small amounts that can cause precision loss
            uint256 smallQuoteAmount = 1; // 1 wei
            uint256 clobResult = clob.getBaseTokenAmount(price, smallQuoteAmount);
            uint256 perfectResult = (smallQuoteAmount * 1e18) / price;

            console.log("  Quote amount:", smallQuoteAmount);
            console.log("  CLOB result:", clobResult);
            console.log("  Perfect result:", perfectResult);

            if (clobResult != perfectResult) {
                console.log("  PRECISION LOSS DETECTED!");
                console.log("  Loss:", perfectResult > clobResult ? perfectResult - clobResult : clobResult - perfectResult);
            }

            // Test reverse calculation
            uint256 smallBaseAmount = 1; // 1 wei
            uint256 clobQuoteResult = clob.getQuoteTokenAmount(price, smallBaseAmount);
            uint256 perfectQuoteResult = (smallBaseAmount * price) / 1e18;

            console.log("  Base amount:", smallBaseAmount);
            console.log("  CLOB quote result:", clobQuoteResult);
            console.log("  Perfect quote result:", perfectQuoteResult);

            if (clobQuoteResult != perfectQuoteResult) {
                console.log("  REVERSE PRECISION LOSS DETECTED!");
                console.log("  Loss:", perfectQuoteResult > clobQuoteResult ? perfectQuoteResult - clobQuoteResult : clobQuoteResult - perfectQuoteResult);
            }

            console.log("---");
        }
    }

    // Test precision loss accumulation over multiple small trades
    function testPrecisionLossAccumulation() public {
        address maker = users[0];
        address taker = users[1];

        // Setup: Use valid price that can still cause precision loss
        uint256 price = TICK_SIZE; // Use minimum tick size
        uint256 makerBaseAmount = 1000e18; // Large maker order

        // Setup maker with large base amount
        setupTokens(Side.SELL, maker, makerBaseAmount, price, true);

        // Place large maker sell order
        vm.prank(maker);
        clob.postLimitOrder(maker, ICLOB.PostLimitOrderArgs({
            amountInBase: makerBaseAmount,
            price: price,
            cancelTimestamp: 0,
            side: Side.SELL,
            clientOrderId: 1,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
        }));

        // Simulate many small trades that accumulate precision loss
        uint256 totalPrecisionLoss = 0;
        uint256 totalCLOBCalculated = 0;
        uint256 totalPerfectCalculated = 0;

        for (uint256 i = 0; i < 100; i++) {
            uint256 smallQuoteAmount = 1000 + i; // Varying small amounts

            // CLOB calculation (with precision loss)
            uint256 clobResult = clob.getBaseTokenAmount(price, smallQuoteAmount);
            totalCLOBCalculated += clobResult;

            // Perfect precision calculation
            uint256 perfectResult = (smallQuoteAmount * 1e18) / price;
            totalPerfectCalculated += perfectResult;

            // Track precision loss for this calculation
            if (perfectResult > clobResult) {
                totalPrecisionLoss += (perfectResult - clobResult);
            }
        }

        console.log("Total CLOB calculated:", totalCLOBCalculated);
        console.log("Total perfect precision:", totalPerfectCalculated);
        console.log("Total precision loss:", totalPrecisionLoss);
        console.log("Precision loss percentage:", (totalPrecisionLoss * 10000) / totalPerfectCalculated, "bps");

        // Demonstrate that precision loss is significant
        if (totalPrecisionLoss > 0) {
            console.log("VULNERABILITY: Cumulative precision loss detected");
            console.log("Loss amount:", totalPrecisionLoss);

            // Test if this loss could be exploited
            if (totalPrecisionLoss > 1e15) { // More than 0.001 tokens lost
                console.log("CRITICAL: Precision loss exceeds dust threshold");
            }
        }
    }

    // Test precision loss in expired order refunds
    function testPrecisionLossInExpiredOrders() public {
        address maker = users[0];

        // Setup: Use problematic price
        uint256 price = 1e12; // Very small price
        uint256 baseAmount = 1500; // Small amount that causes precision loss

        // Setup maker
        setupTokens(Side.SELL, maker, baseAmount, price, true);

        // Place order that will expire
        vm.prank(maker);
        ICLOB.PostLimitOrderResult memory result = clob.postLimitOrder(maker, ICLOB.PostLimitOrderArgs({
            amountInBase: baseAmount,
            price: price,
            cancelTimestamp: uint32(block.timestamp + 1), // Expires in 1 second
            side: Side.SELL,
            clientOrderId: 1,
            limitOrderType: ICLOB.LimitOrderType.GOOD_TILL_CANCELLED
        }));

        // Get initial balance
        uint256 initialBalance = accountManager.getAccountBalance(maker, address(baseToken));
        console.log("Initial balance:", initialBalance);

        // Fast forward time to expire the order
        vm.warp(block.timestamp + 2);

        // Try to place another order to trigger expired order cleanup
        setupTokens(Side.BUY, users[1], 1e18, price, false);
        vm.prank(users[1]);
        clob.postFillOrder(users[1], ICLOB.PostFillOrderArgs({
            amount: 1e15, // Small amount
            priceLimit: price * 2, // Higher price limit
            side: Side.BUY,
            amountIsBase: false,
            fillOrderType: ICLOB.FillOrderType.IMMEDIATE_OR_CANCEL
        }));

        // Check if maker got refunded correctly
        uint256 finalBalance = accountManager.getAccountBalance(maker, address(baseToken));
        console.log("Final balance:", finalBalance);
        console.log("Expected refund:", baseAmount);
        console.log("Actual refund:", finalBalance - initialBalance);

        if (finalBalance != initialBalance + baseAmount) {
            console.log("VULNERABILITY: Precision loss in expired order refund");
            console.log("Loss:", (initialBalance + baseAmount) - finalBalance);
        }
    }
}
