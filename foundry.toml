# Foundry Configuration File
# Default definitions: https://github.com/foundry-rs/foundry/blob/b7917fa8491aedda4dd6db53fbb206ea233cd531/config/src/lib.rs#L782
# See more config options at: https://github.com/foundry-rs/foundry/tree/master/.config

# The Default Profile
[profile.default]
solc_version = "0.8.28"
evm_version = "paris" # Cancun will be tested in the CI.
auto_detect_solc = false
optimizer = true
optimizer_runs = 1_000
gas_limit = 100_000_000 # ETH is 30M, but we use a higher value.
skip = ["/*Transient*", "*/ext/ithaca/*"]
fs_permissions = [{ access = "read", path = "./test/data"}]
remappings = [
  "forge-std=test/utils/forge-std/"
]

[profile.pre_global_structs]
skip = ["*/g/*", "/*Transient*", "*/ext/ithaca/*"]

[profile.post_cancun]
evm_version = "cancun"
skip = ["*/ext/ithaca/*"]

[profile.ithaca]
match_path = "*/ext/ithaca/*"
evm_version = "cancun"
odyssey = true
skip = []

[fmt]
line_length = 100 # While we allow up to 120, we lint at 100 for readability.

[profile.default.fuzz]
runs = 256

[invariant]
depth = 15
runs = 10
