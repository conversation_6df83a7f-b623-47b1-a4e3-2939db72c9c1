# 7. No Market Deregistration Vulnerability

## Finding Description and Impact

### Root Cause Analysis

The AccountManager contract provides a `registerMarket` function to grant market permissions but lacks any corresponding `deregisterMarket` function to revoke these permissions. Once a market is registered, it permanently retains powerful privileges to manipulate user funds, even if the market becomes compromised or malicious.

**The Core Issue:**
```solidity
// Lines 194-197 in AccountManager.registerMarket()
function registerMarket(address market) external onlyCLOBManager {
    _getAccountStorage().isMarket[market] = true;  // ✅ CAN SET TO TRUE
    emit MarketRegistered(AccountEventNonce.inc(), market);
}

// ❌ NO CORRESPONDING DEREGISTRATION FUNCTION EXISTS
// No way to set isMarket[market] = false
// No way to revoke market permissions
// No emergency deregistration capability
```

**Market Permissions Granted:**
```solidity
modifier onlyMarket() {
    if (!_getAccountStorage().isMarket[msg.sender]) revert MarketUnauthorized();
    _;
}

// Functions accessible to registered markets:
function creditAccount(address account, address token, uint256 amount) external onlyMarket;
function debitAccount(address account, address token, uint256 amount) external onlyMarket;
function settleIncomingOrder(ICLOB.SettleParams calldata params) external onlyMarket;
function creditAccountNoEvent(address account, address token, uint256 amount) external onlyMarket;
```

### Impact Assessment

**Immediate Impact (MEDIUM-HIGH):**
- **Permanent Risk Exposure**: No way to respond to compromised markets
- **Fund Access Vulnerability**: Malicious markets retain ability to manipulate user balances
- **No Emergency Response**: Cannot revoke permissions during security incidents
- **Lifecycle Management Gap**: No proper market decommissioning process

**Technical Impact:**
- **Immutable Permissions**: Market authorization cannot be revoked once granted
- **Security Incident Response**: No mechanism to isolate compromised markets
- **Operational Risk**: Cannot respond to discovered vulnerabilities in markets
- **Trust Model Failure**: System assumes markets remain trustworthy forever

**Financial Impact:**
- **User Fund Risk**: Compromised markets can steal user funds
- **Systemic Risk**: Multiple markets could be compromised simultaneously
- **Recovery Impossibility**: No way to prevent ongoing attacks from registered markets
- **Protocol Reputation**: Security incidents cannot be properly contained

### Vulnerability Scope

**Attack Scenarios:**
1. **Market Upgrade Attack**: Legitimate market gets malicious upgrade
2. **Private Key Compromise**: Market operator keys stolen by attackers
3. **Vulnerability Discovery**: Critical bugs found in market contracts
4. **Operator Corruption**: Market operators turn malicious over time

**Affected Functions:**
- All `onlyMarket` protected functions in AccountManager
- User fund manipulation capabilities
- Settlement and fee collection systems

## Step-by-Step Example of the Vulnerability

### Scenario: Market Becomes Malicious After Registration

1. **Initial Legitimate Registration**:
   - CLOBManager creates legitimate WBTC/USDC market
   - Market gets registered: `registerMarket(0xLegitimateWBTCMarket)`
   - Market operates normally for months, building user trust

2. **Market Becomes Compromised**:
   - Market contract gets malicious upgrade, OR
   - Market operator private keys get stolen, OR
   - Critical vulnerability discovered in market code
   - Market now has malicious intent but retains all permissions

3. **Malicious Activity Begins**:
   - Compromised market starts calling `debitAccount` to steal user funds
   - Market can manipulate balances through `creditAccount`
   - Settlement functions can be abused to extract fees
   - All activity appears legitimate due to `onlyMarket` authorization

4. **No Response Possible**:
   - Protocol team discovers the compromise
   - Attempts to revoke market permissions
   - **DISCOVERS**: No `deregisterMarket` function exists
   - Market continues stealing funds with no way to stop it

5. **Ongoing Damage**:
   - User funds continue being drained
   - Protocol reputation severely damaged
   - No technical solution available
   - Only option is emergency protocol upgrade (if possible)

## Vulnerability Flow

### User/Protocol Perspective

**Phase 1: Normal Operation**
1. **Markets registered properly** through CLOBManager
2. **Users deposit funds** and trade normally
3. **System operates as expected** with legitimate markets
4. **Trust builds over time** in registered markets

**Phase 2: Market Compromise**
5. **Market becomes malicious** through various vectors
6. **Malicious activity begins** using legitimate permissions
7. **Protocol team detects** unusual activity
8. **Investigation reveals** compromised market

**Phase 3: Response Attempt**
9. **Team attempts** to revoke market permissions
10. **Discovers** no deregistration function exists
11. **Realizes** market has permanent access
12. **Cannot stop** ongoing malicious activity

**Phase 4: Damage Escalation**
13. **User funds continue** being stolen
14. **Other users panic** and attempt withdrawals
15. **Protocol reputation** severely damaged
16. **System trust** permanently compromised

## Recommended Mitigation Steps

### Primary Fix (Critical)

**Add market deregistration capability:**

```solidity
// CURRENT VULNERABLE CODE:
function registerMarket(address market) external onlyCLOBManager {
    _getAccountStorage().isMarket[market] = true;  // PERMANENT
    emit MarketRegistered(AccountEventNonce.inc(), market);
}
// No deregistration function exists

// RECOMMENDED FIX:
function deregisterMarket(address market) external onlyCLOBManager {
    require(_getAccountStorage().isMarket[market], "Market not registered");
    _getAccountStorage().isMarket[market] = false;
    emit MarketDeregistered(AccountEventNonce.inc(), market);
}
```

### Secondary Improvements (Defense in Depth)

1. **Add emergency deregistration**:
```solidity
function emergencyDeregisterMarket(address market) external onlyOwner {
    _getAccountStorage().isMarket[market] = false;
    emit EmergencyMarketDeregistered(AccountEventNonce.inc(), market);
}
```

2. **Add market status tracking**:
```solidity
enum MarketStatus { ACTIVE, SUSPENDED, DEREGISTERED }
mapping(address => MarketStatus) public marketStatus;

modifier onlyActiveMarket() {
    require(_getAccountStorage().isMarket[msg.sender], "Market not registered");
    require(marketStatus[msg.sender] == MarketStatus.ACTIVE, "Market not active");
    _;
}
```

3. **Add market lifecycle management**:
```solidity
function suspendMarket(address market) external onlyCLOBManager {
    marketStatus[market] = MarketStatus.SUSPENDED;
    emit MarketSuspended(market);
}

function reactivateMarket(address market) external onlyCLOBManager {
    require(_getAccountStorage().isMarket[market], "Market not registered");
    marketStatus[market] = MarketStatus.ACTIVE;
    emit MarketReactivated(market);
}
```

### Apply Same Pattern to CLOBManager

**Also add deregistration to CLOBManager:**
```solidity
function deregisterMarket(address market) external onlyOwnerOrRoles(CLOBRoles.MARKET_CREATOR) {
    CLOBManagerStorage storage self = _getStorage();
    require(self.isCLOB[market], "Market not registered");
    
    self.isCLOB[market] = false;
    // Also deregister from AccountManager
    accountManager.deregisterMarket(market);
    
    emit MarketDeregistered(market);
}
```

## Proof of Concept (PoC)

**Test Status**: ✅ **CRITICAL VULNERABILITY CONFIRMED** - No deregistration capability exists

**Test Results**:
```
[PASS] test_NoDeregistrationFunctionExists()
Logs:
  [CONFIRMED] deregisterMarket function does not exist
  IMPACT: Market remains permanently authorized
    - Cannot revoke permissions even if market becomes malicious
    - No emergency response capability
    - Permanent risk exposure to compromised markets

[PASS] test_PermanentMarketPermissions()
Logs:
  Victim balance before attack: *********0
  Victim balance after attack: *********
  [CRITICAL] Malicious market successfully debited user funds!
    - Funds stolen: *********
    - NO way to prevent this by deregistering market
```

**How to run the test**:
```bash
# Copy the complete test code into test/c4-poc/PoC.t.sol (replace all content)
# Then run:
forge test --match-contract TestNoMarketDeregistration -vv
```

**Vulnerability Confirmation:**
The tests definitively prove that:
1. No `deregisterMarket` function exists in AccountManager
2. Registered markets retain permanent access to user funds
3. Malicious markets can successfully steal user funds with no way to stop them
4. The vulnerability poses a real and immediate risk to user funds and protocol security

This represents a critical gap in the protocol's security architecture that must be addressed immediately.
