# Universal Vulnerability Analysis Framework Prompt

## Overview
This prompt provides a comprehensive methodology for conducting systematic vulnerability analysis on ANY codebase, regardless of:
- **Programming Language** (Solidity, Rust, Go, TypeScript, Python, etc.)
- **Blockchain Platform** (Ethereum, Solana, Cosmos, Polkadot, etc.)
- **Protocol Type** (DeFi, GameFi, NFT, Infrastructure, etc.)
- **Architecture** (Smart contracts, Programs, Modules, Services, etc.)

Follow this step-by-step framework to uncover critical vulnerabilities through realistic scenario analysis, multi-flow attack vectors, and comprehensive testing that adapts to any technology stack.

## Phase 1: Project Structure Setup

### Step 1.1: Create Main Analysis Folder
```
Create folder: aarc/
```

### Step 1.2: Create Universal Analysis Directories
```
Create the following folder structure (adapts to any codebase):
aarc/
├── flow-digest/                    # Individual function/method/instruction flow analyses
├── compare-flow-digest/            # Function/method comparison analyses
├── scenarios/                      # Attack scenario testing
├── report-review/                  # Vulnerability findings and reports
└── README.md                       # Project overview
```

**Language-Specific Adaptations:**
- **Solidity**: Functions, modifiers, events
- **Rust/Solana**: Instructions, accounts, programs
- **Go**: Functions, methods, interfaces
- **TypeScript**: Functions, classes, modules
- **Python**: Functions, classes, methods

## Phase 2: Individual Function/Method Flow Analysis

### Step 2.1: Identify Core Functions (Universal Approach)
Identify the 6 most critical functions/methods/instructions in ANY codebase:

#### **For DeFi/Financial Protocols:**
1. **Asset Entry Function** (deposit, stake, mint)
2. **Primary Action Function** (trade, swap, lend, borrow)
3. **Modification Function** (amend, update, adjust)
4. **Execution Function** (execute, process, settle)
5. **Cancellation Function** (cancel, revoke, abort)
6. **Asset Exit Function** (withdraw, unstake, burn)

#### **For GameFi/NFT Protocols:**
1. **Asset Creation Function** (mint, create, spawn)
2. **Asset Transfer Function** (transfer, trade, gift)
3. **Asset Modification Function** (upgrade, enhance, modify)
4. **Game Action Function** (play, battle, compete)
5. **Asset Destruction Function** (burn, destroy, consume)
6. **Reward Distribution Function** (claim, distribute, harvest)

#### **For Infrastructure/Governance:**
1. **Registration Function** (register, join, enroll)
2. **Proposal Function** (propose, submit, create)
3. **Voting Function** (vote, delegate, support)
4. **Execution Function** (execute, implement, deploy)
5. **Update Function** (upgrade, modify, patch)
6. **Removal Function** (remove, delete, deregister)

#### **For Any Other Protocol:**
1. **Primary Entry Point** (main user interaction)
2. **Core Business Logic** (main protocol function)
3. **State Modification** (changes system state)
4. **External Integration** (calls other systems)
5. **Administrative Function** (privileged operations)
6. **Exit/Cleanup Function** (resource cleanup)

### Step 2.2: Create Individual Flow Analysis Files (Universal)
For each function/method/instruction, create a numbered flow analysis file:
- `Flow 01: [function-name].md` (Primary Entry Point)
- `Flow 02: [function-name].md` (Core Business Logic)
- `Flow 03: [function-name].md` (State Modification)
- `Flow 04: [function-name].md` (External Integration)
- `Flow 05: [function-name].md` (Administrative Function)
- `Flow 06: [function-name].md` (Exit/Cleanup Function)

**Language-Specific Examples:**
- **Solidity**: `Flow 01: deposit.md`, `Flow 02: swap.md`
- **Rust/Solana**: `Flow 01: initialize.md`, `Flow 02: process_instruction.md`
- **Go**: `Flow 01: HandleRequest.md`, `Flow 02: ProcessTransaction.md`
- **TypeScript**: `Flow 01: createUser.md`, `Flow 02: updateBalance.md`

### Step 2.3: Universal Flow Analysis Template
Each flow analysis must include (adapts to any language/protocol):

```markdown
# Flow [XX]: [Function/Method/Instruction Name] Analysis

## Function Overview (Universal)
- **Purpose**: [What the function/method/instruction does]
- **Parameters**: [All input parameters/arguments with types]
- **Access Control**: [Who can call it and restrictions]
- **External Calls**: [Any external system/contract/service calls]
- **Language**: [Programming language and platform]
- **Context**: [Smart contract/program/service/module context]

## Realistic Scenario Setup (Protocol-Specific)

## Universal User Personas (Fun & Memorable Characters)

### 🎭 **Complete Cast of Characters** (Use relevant ones for your protocol):

#### **👤 Regular Users:**
- **AliceCool** 🌟 (Casual User): Regular everyday user, small to medium transactions
- **BobBuilder** 🔨 (Developer/Integrator): Building on top of the protocol, testing integrations
- **CarlaCreator** 🎨 (Content Creator): Creates content, NFTs, or digital assets
- **DaveDaily** 📅 (Regular Trader): Daily active user with consistent patterns

#### **🐋 Power Users:**
- **BobWhale** 🐋 (High Volume): Large volume user, major liquidity provider
- **FrankFlash** ⚡ (Speed Trader): High-frequency operations, time-sensitive trades
- **GraceGuild** 👑 (Guild Leader): Manages multiple accounts, coordinates group activities
- **HenryHodler** 💎 (Long-term Holder): Large positions, infrequent but significant operations

#### **🤖 Automated Users:**
- **CharlieBot** 🤖 (Trading Bot): Automated trading operations, MEV extraction
- **RobotRita** 🔄 (Automation): Automated workflows, scheduled operations
- **SamScanner** 🔍 (Monitor Bot): Scans for opportunities, arbitrage detection
- **TechTina** 💻 (API User): Heavy API usage, programmatic interactions

#### **🏛️ Institutional Users:**
- **DianaValidator** ⚖️ (Validator/Node): Network validation, consensus participation
- **InvestorIvan** 🏦 (Institution): Large institutional user, compliance requirements
- **ManagerMike** 📊 (Fund Manager): Managing multiple client accounts
- **TreasuryTom** 🏛️ (DAO Treasury): Managing protocol treasury, governance operations

#### **👮 Administrative Users:**
- **AdminAnna** 👮‍♀️ (Administrator): Protocol admin, emergency powers
- **ModeratorMax** 🛡️ (Moderator): Content moderation, user management
- **OperatorOllie** ⚙️ (Operator): Day-to-day operations, maintenance tasks
- **GovernorGary** 🗳️ (Governance): Proposal creation, voting coordination

#### **🎮 Gaming-Specific Users:**
- **PlayerPete** 🎮 (Casual Gamer): Regular gaming user, moderate spending
- **WhaleWanda** 🐋🎮 (Gaming Whale): High-spending player, premium items
- **GamerGina** 🏆 (Competitive): Competitive player, tournament participation
- **CollectorCole** 🃏 (NFT Collector): Collects rare items, marketplace activity

#### **🔒 Security-Focused Users:**
- **EveExploiter** 😈 (Malicious Actor): Primary attacker, vulnerability exploitation
- **HackerHank** 💀 (Advanced Attacker): Sophisticated attacks, multi-vector exploitation
- **GrieferGabe** 😠 (Disruptor): Causes disruption, griefing attacks
- **ScammerSue** 🎭 (Social Engineer): Phishing, social engineering attacks

#### **🔧 Technical Users:**
- **DevDan** 👨‍💻 (Developer): Protocol developer, internal testing
- **TesterTara** 🧪 (QA Tester): Quality assurance, edge case testing
- **AuditorAlex** 🔍 (Security Auditor): Security testing, vulnerability assessment
- **IntegratorIvy** 🔗 (Integration): Third-party integrations, external connections

### 📋 **Persona Selection Guide:**

#### **For DeFi/Financial Protocols:**
**Primary Cast**: AliceCool, BobWhale, CharlieBot, DianaValidator, EveExploiter
**Supporting Cast**: FrankFlash, InvestorIvan, TreasuryTom, AdminAnna, HackerHank

#### **For GameFi/NFT Protocols:**
**Primary Cast**: PlayerPete, WhaleWanda, GamerGina, CollectorCole, EveExploiter
**Supporting Cast**: CarlaCreator, GraceGuild, ModeratorMax, ScammerSue, GrieferGabe

#### **For Infrastructure/Governance:**
**Primary Cast**: DianaValidator, GovernorGary, OperatorOllie, AdminAnna, EveExploiter
**Supporting Cast**: TreasuryTom, ManagerMike, TechTina, AuditorAlex, HackerHank

#### **For Web Services/APIs:**
**Primary Cast**: AliceCool, BobBuilder, TechTina, AdminAnna, EveExploiter
**Supporting Cast**: DevDan, TesterTara, IntegratorIvy, RobotRita, HackerHank

#### **For Mobile/Desktop Apps:**
**Primary Cast**: AliceCool, DaveDaily, ModeratorMax, AdminAnna, EveExploiter
**Supporting Cast**: BobBuilder, TesterTara, DevDan, ScammerSue, GrieferGabe

### 💰 **Realistic Values by Protocol Type:**

### 🏦 **For DeFi/Financial Protocols:**
**Character Examples**: AliceCool, BobWhale, CharlieBot, DianaValidator, EveExploiter
- **AliceCool's Deposit**: $20,000 USDC (casual but significant amount)
- **BobWhale's Position**: $500,000 USDC (major liquidity provider)
- **CharlieBot's Trade**: $15,000 worth (5 ETH at $3,000)
- **DianaValidator's Stake**: $100,000 worth (validator requirements)
- **EveExploiter's Attack**: $50,000 worth (significant exploit attempt)
- **Fee Structure**: 0.3% trading fee, 0.05% withdrawal fee
- **Slippage Tolerance**: 0.5% for AliceCool, 0.1% for BobWhale

### 🎮 **For GameFi/NFT Protocols:**
**Character Examples**: PlayerPete, WhaleWanda, GamerGina, CollectorCole, EveExploiter
- **PlayerPete's Purchase**: 0.5 ETH NFT ($1,500 casual gaming item)
- **WhaleWanda's Collection**: 50 ETH worth ($150,000 rare collection)
- **GamerGina's Tournament**: 10,000 game tokens entry fee
- **CollectorCole's Bid**: 5 ETH ($15,000 for rare NFT)
- **EveExploiter's Exploit**: Duplicate 100 ETH worth of assets
- **Game Economics**: 1,000 tokens = $100, 10% marketplace fee
- **Battle Stakes**: 500 tokens ($50), Winner takes 900 tokens

### 🏛️ **For Infrastructure/Governance:**
**Character Examples**: DianaValidator, GovernorGary, TreasuryTom, AdminAnna, EveExploiter
- **DianaValidator's Stake**: 1,000,000 tokens ($100,000 validator bond)
- **GovernorGary's Proposal**: 50,000 tokens threshold ($5,000 proposal fee)
- **TreasuryTom's Management**: 10,000,000 tokens ($1M treasury)
- **AdminAnna's Emergency**: Protocol pause/unpause powers
- **EveExploiter's Attack**: 51% governance attack (5,000,001 tokens)
- **Voting Parameters**: 7-day voting period, 40% quorum requirement
- **Delegation**: Minimum 1,000 tokens, maximum 10% of supply

### 💻 **For Web Services/APIs:**
**Character Examples**: AliceCool, BobBuilder, TechTina, AdminAnna, EveExploiter
- **AliceCool's Usage**: 1,000 API calls/day (standard user)
- **BobBuilder's Integration**: 50,000 API calls/day (developer tier)
- **TechTina's Automation**: 500,000 API calls/day (enterprise bot)
- **AdminAnna's Management**: Unlimited calls, admin dashboard access
- **EveExploiter's Attack**: 10M API calls/minute (DDoS attempt)
- **Rate Limits**: 100/min free, 1,000/min paid, 10,000/min enterprise
- **Data Volumes**: 1GB/user free, 100GB/user paid, unlimited enterprise

### 📱 **For Mobile/Desktop Apps:**
**Character Examples**: AliceCool, DaveDaily, ModeratorMax, AdminAnna, EveExploiter
- **AliceCool's Activity**: 50 actions/day (casual user)
- **DaveDaily's Usage**: 500 actions/day (power user)
- **ModeratorMax's Work**: 2,000 moderation actions/day
- **AdminAnna's Control**: Full app configuration access
- **EveExploiter's Attack**: 100,000 spam actions/minute
- **Storage Limits**: 1GB free, 10GB premium, unlimited admin
- **Feature Access**: Basic free, Premium $10/month, Admin $100/month

### 🔧 **For System Software/Infrastructure:**
**Character Examples**: OperatorOllie, DevDan, TesterTara, AdminAnna, EveExploiter
- **OperatorOllie's Load**: 10,000 transactions/second processing
- **DevDan's Testing**: 1,000 test cases, 100GB test data
- **TesterTara's Stress**: 1M concurrent connections test
- **AdminAnna's Resources**: Full system access, unlimited quotas
- **EveExploiter's Attack**: Buffer overflow with 10MB payload
- **System Limits**: 1TB storage, 64GB RAM, 32 CPU cores
- **Performance**: 99.9% uptime SLA, <100ms response time
- **Modification Amount**: $9,600 worth (reduced amount)
- **Execution Amount**: $8,100 worth (partial execution)
- **Fee Amount**: 0.3% of transaction value
- **Slippage**: 0.5% maximum acceptable

### For GameFi/NFT Protocols:
#### User Personas:
- **Alice** (Casual Player): Regular gaming user
- **Bob** (Whale Player): High-spending user
- **Charlie** (Bot Operator): Automated gaming operations
- **Diana** (Guild Leader): Managing multiple accounts
- **Eve** (Exploiter): Looking for game exploits

#### Realistic Values:
- **NFT Price**: 5 ETH ($15,000)
- **Game Token Amount**: 10,000 tokens
- **Upgrade Cost**: 1,000 tokens
- **Battle Stake**: 500 tokens
- **Reward Amount**: 200 tokens
- **Marketplace Fee**: 2.5%

### For Infrastructure/Governance:
#### User Personas:
- **Alice** (Regular Voter): Standard governance participant
- **Bob** (Delegate): Manages delegated voting power
- **Charlie** (Proposer): Creates governance proposals
- **Diana** (Validator): Network validator/operator
- **Eve** (Attacker): Governance attack attempts

#### Realistic Values:
- **Voting Power**: 100,000 tokens
- **Proposal Threshold**: 10,000 tokens
- **Execution Delay**: 7 days
- **Quorum Requirement**: 40%
- **Proposal Fee**: 1,000 tokens
- **Delegation Amount**: 50,000 tokens

## Step-by-Step Function/Method Execution (Universal)
### Pre-Execution State:
[Document exact state before function/method/instruction call]
- **Storage/Memory State**: Current values of all relevant variables
- **Account/User State**: Balances, permissions, status
- **System State**: Global protocol state, external dependencies
- **Access Context**: Who is calling and with what permissions

### Line-by-Line Analysis (Language-Agnostic):
For each line of code/instruction:
- **Line [X]**: `[actual code/instruction]`
- **Language Context**: [Solidity/Rust/Go/TypeScript specific behavior]
- **Action**: [What this line does in plain English]
- **Values**: [Actual values with realistic amounts for the protocol type]
- **State Change**: [How state changes - storage, memory, accounts]
- **External Interactions**: [Calls to other contracts/programs/services]
- **Security Checks**: [Validations, access controls, bounds checking]
- **Potential Issues**: [Any vulnerabilities spotted at this line]

### Post-Execution State:
[Document exact state after function/method/instruction call]
- **Storage/Memory Changes**: What changed and by how much
- **Account/User Changes**: Balance updates, permission changes
- **System Changes**: Global state modifications
- **Events/Logs**: What events were emitted/logged
- **Return Values**: What the function returns

## Vulnerability Analysis (Universal)
### Identified Issues:
1. **[Vulnerability Category]**: [Description and impact]
   - **Language-Specific Risk**: [How this manifests in the specific language]
   - **Protocol-Specific Impact**: [How this affects the specific protocol type]
2. **[Vulnerability Category]**: [Description and impact]

### Attack Vectors:
[Specific ways this function can be exploited in this language/protocol]
- **Direct Attacks**: [Direct exploitation methods]
- **Indirect Attacks**: [Attacks through other functions/methods]
- **Cross-Function Attacks**: [Attacks involving multiple functions]
- **External Attacks**: [Attacks involving external systems]

## Integration Points (Universal)
[How this function interacts with other functions/methods/systems]
- **Internal Dependencies**: [Other functions in same contract/program/service]
- **External Dependencies**: [Other contracts/programs/services called]
- **State Dependencies**: [Shared state variables/storage]
- **Event Dependencies**: [Events/logs that other systems depend on]
- **Access Control Dependencies**: [Permission systems involved]
```

### Step 2.4: Universal Critical Analysis Requirements
For each function/method/instruction, you must:

#### **Language-Agnostic Requirements:**
1. **Use consistent realistic values** across all analyses (appropriate for protocol type)
2. **Follow actual code execution** line by line (regardless of language)
3. **Calculate exact amounts** with proper precision handling (decimals, integers, floating point)
4. **Identify state changes** at each step (storage, memory, accounts, databases)
5. **Spot potential vulnerabilities** in real-time (language-specific and universal)
6. **Document integration points** with other functions/methods/services

#### **Language-Specific Considerations:**

**For Solidity/EVM:**
- Track gas consumption and optimization opportunities
- Identify reentrancy vulnerabilities and external calls
- Check for integer overflow/underflow (pre/post Solidity 0.8.0)
- Analyze storage layout and slot packing
- Verify access control modifiers and permissions

**For Rust/Solana:**
- Track account ownership and signer requirements
- Identify program derived address (PDA) vulnerabilities
- Check for arithmetic overflow with explicit handling
- Analyze account data serialization/deserialization
- Verify cross-program invocation (CPI) security

**For Go/Cosmos:**
- Track keeper permissions and module interactions
- Identify state machine transition vulnerabilities
- Check for panic conditions and error handling
- Analyze inter-blockchain communication (IBC) security
- Verify governance and upgrade mechanisms

**For TypeScript/JavaScript:**
- Track async/await patterns and race conditions
- Identify prototype pollution and injection vulnerabilities
- Check for type coercion and validation bypasses
- Analyze API endpoint security and authentication
- Verify database query injection and sanitization

**For Python:**
- Track import and dependency vulnerabilities
- Identify pickle/serialization security issues
- Check for SQL injection and command injection
- Analyze file system access and path traversal
- Verify cryptographic implementation correctness

## Phase 3: Universal Comparative Function/Method Analysis

### Step 3.1: Create Universal Comparison Files
Create numbered comparison files in `compare-flow-digest/`:
- `01-[function1]-vs-[function2].md` (Entry vs Exit functions)
- `02-[function3]-vs-[function4].md` (Core business logic functions)
- `03-[function5]-vs-[function6].md` (Administrative vs User functions)

**Language-Specific Examples:**
- **Solidity**: `01-deposit-vs-withdraw.md`, `02-mint-vs-burn.md`
- **Rust/Solana**: `01-initialize-vs-close.md`, `02-process-vs-validate.md`
- **Go**: `01-CreateUser-vs-DeleteUser.md`, `02-ProcessTx-vs-ValidateTx.md`
- **TypeScript**: `01-login-vs-logout.md`, `02-create-vs-update.md`

### Step 3.2: Universal Comparison Analysis Template
```markdown
# Compare [XX]: [Function1] vs [Function2] - Universal Symmetry and Asymmetry Analysis

## Function/Method Overview (Language-Agnostic)
[Brief description of both functions/methods/instructions]
- **Language**: [Programming language and platform]
- **Context**: [Smart contract/program/service/module context]
- **Purpose**: [What each function is supposed to do]

## Structural Comparison (Universal)
### Function/Method Signatures
[Compare signatures, parameters, return types across languages]
- **Parameter Types**: [Compare input parameter types and validation]
- **Return Types**: [Compare output types and error handling]
- **Access Modifiers**: [Compare visibility and permission requirements]
- **Language-Specific Features**: [Modifiers, decorators, attributes, etc.]

### Operation Sequence
[Compare the order of operations regardless of language]
- **Validation Phase**: [How each function validates inputs]
- **State Modification Phase**: [How each function changes state]
- **External Interaction Phase**: [How each function interacts with external systems]
- **Cleanup/Finalization Phase**: [How each function completes execution]

## Symmetry Analysis (Universal)
### ✅ Perfect Symmetries
[List identical patterns between functions regardless of language]
- **Logic Flow**: [Same logical steps and decision points]
- **State Changes**: [Equivalent state modifications]
- **Error Handling**: [Similar error detection and handling]
- **Security Checks**: [Equivalent validation and access control]

### ⚖️ Functional Symmetries
[List functionally equivalent but different implementations]
- **Different Syntax, Same Logic**: [Language-specific implementations of same concept]
- **Equivalent Validations**: [Different ways of checking same conditions]
- **Similar State Updates**: [Different approaches to same state changes]
- **Comparable External Calls**: [Different ways of interacting with external systems]

## Asymmetry Analysis (Critical for Any Language)
### 🚨 Critical Asymmetries
[Identify dangerous differences that can be exploited in any language]
- **Operation Order Differences**: [Different sequence of operations]
- **Error Handling Patterns**: [Fail-fast vs graceful failure differences]
- **Validation Strength**: [Strict vs lenient input validation]
- **State Consistency**: [Different approaches to maintaining state]
- **Access Control**: [Different permission checking mechanisms]

### 🔍 Language-Specific Asymmetries
[Unexpected differences specific to the programming language]
- **Memory Management**: [Different approaches to resource handling]
- **Concurrency Handling**: [Different thread safety or async patterns]
- **Type System Usage**: [Different type checking or conversion approaches]
- **Error Propagation**: [Different exception/error handling mechanisms]

## Security Implications (Universal)
[How asymmetries can be exploited regardless of language]
- **Race Condition Opportunities**: [Timing-based attack vectors]
- **State Corruption Possibilities**: [Ways to create inconsistent state]
- **Access Control Bypasses**: [Permission escalation opportunities]
- **Resource Exhaustion**: [DoS attack possibilities]
- **Logic Bomb Potential**: [Hidden malicious behavior triggers]

## Attack Scenarios (Language-Agnostic)
[Specific attacks leveraging asymmetries that work across languages]
- **Cross-Function Exploitation**: [Using differences between functions]
- **State Transition Attacks**: [Exploiting different state handling]
- **Validation Bypass**: [Using weaker validation in one function]
- **Privilege Escalation**: [Exploiting access control differences]

## Recommendations (Universal)
[How to fix asymmetric vulnerabilities regardless of language]
- **Standardize Patterns**: [Make similar functions follow same patterns]
- **Unify Validation**: [Use consistent input validation approaches]
- **Harmonize Error Handling**: [Implement consistent error handling]
- **Align Access Control**: [Use uniform permission checking]
- **Document Differences**: [Clearly document intentional asymmetries]
```

### Step 3.3: Universal Key Asymmetries to Identify
Look for these patterns across ANY programming language and protocol:

#### **Universal Asymmetry Categories:**
1. **Operation Order Differences**
   - Credit-before-transfer vs debit-before-transfer
   - Validate-then-execute vs execute-then-validate
   - Lock-then-modify vs modify-then-lock

2. **Error Handling Patterns**
   - Fail-fast vs graceful failure
   - Exception throwing vs error return codes
   - Rollback vs continue-on-error

3. **Validation Differences**
   - Strict vs lenient input validation
   - Client-side vs server-side validation
   - Pre-condition vs post-condition checking

4. **State Management Variations**
   - Immediate vs deferred state updates
   - Atomic vs non-atomic operations
   - Persistent vs temporary state changes

5. **Access Control Inconsistencies**
   - Different permission checking mechanisms
   - Varying privilege requirements
   - Inconsistent role-based access

#### **Language-Specific Asymmetries:**

**For Solidity/EVM:**
- Gas optimization differences between similar functions
- Different reentrancy protection patterns
- Varying storage vs memory usage patterns
- Different event emission strategies

**For Rust/Solana:**
- Different account ownership validation approaches
- Varying program derived address (PDA) generation
- Different cross-program invocation patterns
- Inconsistent account data serialization

**For Go/Cosmos:**
- Different keeper permission patterns
- Varying module interaction approaches
- Different state machine transition handling
- Inconsistent inter-blockchain communication

**For TypeScript/JavaScript:**
- Different async/await vs Promise patterns
- Varying type checking approaches
- Different API authentication methods
- Inconsistent database interaction patterns

**For Python:**
- Different exception handling strategies
- Varying import and dependency patterns
- Different serialization approaches
- Inconsistent database ORM usage

## Phase 4: Universal Advanced Attack Scenario Development

### Step 4.1: Create Universal Scenario Files
Create numbered scenario files in `scenarios/` (adapts to any language/protocol):
- `01-reverse-flow-scenarios.md` (Unexpected operation sequences)
- `02-malicious-user-scenarios.md` (Adversarial user behavior)
- `03-multi-flow-attack-scenarios.md` (Coordinated multi-function attacks)
- `04-concurrent-operation-chaos.md` (Race conditions and timing attacks)
- `05-asymmetric-exploitation-patterns.md` (Leveraging function differences)
- `06-temporal-attack-vectors.md` (Time-based vulnerabilities)
- `07-ultimate-protocol-stress-test.md` (Maximum damage scenarios)

**These scenarios work for ANY programming language and protocol type:**
- **Smart Contracts** (Solidity, Rust, Vyper)
- **Traditional Services** (Go, TypeScript, Python, Java)
- **Mobile Applications** (Swift, Kotlin, React Native)
- **Desktop Applications** (C++, C#, Electron)
- **Embedded Systems** (C, Rust, Assembly)

### Step 4.2: Universal Scenario Development Framework

#### Reverse Flow Scenarios (Works for Any Protocol)
Test unexpected operation sequences that violate normal flow:

**For DeFi/Financial Protocols:**
- Withdraw before deposit, Trade before deposit
- Cancel non-existent orders, Amend non-existent orders
- Multiple deposits without trading, Liquidate before borrowing

**For GameFi/NFT Protocols:**
- Use items before acquiring, Battle without registration
- Claim rewards before earning, Upgrade non-existent assets
- Transfer locked assets, Burn borrowed NFTs

**For Infrastructure/Governance:**
- Vote before registration, Execute before proposal
- Delegate before staking, Slash before validation
- Upgrade before initialization, Remove before creation

**For Any Protocol:**
- Exit before entry, Modify before creation
- Delete before existence check, Use before initialization
- Cleanup before setup, Finalize before start

#### Multi-Flow Attack Scenarios (Universal Approach)
Combine multiple operations simultaneously across any language:

**State Corruption Attacks:**
- Atomic state corruption (entry + exit + modify simultaneously)
- Cross-function state inconsistency
- Partial state update exploitation
- State machine bypass attacks

**Race Condition Attacks:**
- Concurrent operations on same resources
- Time-of-check vs time-of-use vulnerabilities
- Atomic operation violations
- Critical section bypasses

**Privilege Escalation Attacks:**
- Cross-function privilege inheritance
- Role-based access control bypasses
- Permission escalation chains
- Administrative function exploitation

#### Concurrent Operation Chaos (Universal)
Test extreme concurrent access patterns across any system:

**Resource Contention Attacks:**
- Thundering herd attacks (1000+ simultaneous operations)
- Deadlock creation through resource competition
- Starvation attacks on shared resources
- Lock contention exploitation

**System Overload Attacks:**
- Memory exhaustion through concurrent operations
- CPU exhaustion through computational bombs
- Network bandwidth saturation
- Database connection pool exhaustion

**Data Race Exploitation:**
- Concurrent modification of shared data
- Read-modify-write race conditions
- Cache coherency violations
- Atomic operation bypasses

#### Asymmetric Exploitation (Language-Agnostic)
Leverage function/method differences across any codebase:

**Logic Asymmetry Exploitation:**
- Error handling pattern differences
- Operation order asymmetric attacks
- Validation strength differences
- State consistency variations

**Implementation Asymmetry Exploitation:**
- Batch vs single operation advantages
- Return type confusion attacks
- Parameter validation bypasses
- Access control inconsistencies

**Performance Asymmetry Exploitation:**
- Resource consumption differences
- Optimization level variations
- Caching behavior differences
- Network latency exploitation

#### Temporal Attack Vectors (Universal)
Exploit time-based vulnerabilities in any system:

**Timing-Based Attacks:**
- Race condition exploitation
- Time-of-check vs time-of-use attacks
- Timing side-channel attacks
- Clock synchronization exploitation

**Sequence-Based Attacks:**
- Operation ordering manipulation
- Event sequence exploitation
- State transition timing attacks
- Workflow bypass through timing

**Persistence-Based Attacks:**
- Long-term state corruption
- Delayed execution exploitation
- Time-bomb logic activation
- Gradual system degradation

### Step 4.3: Universal Attack Scenario Template
```markdown
# Scenario [XX]: [Attack Category] - Universal Analysis

## Overview (Language-Agnostic)
[Description of attack category and objectives that apply to any codebase]
- **Target Systems**: [Types of systems this attack applies to]
- **Programming Languages**: [Languages where this attack is relevant]
- **Protocol Types**: [Protocol categories vulnerable to this attack]

## Scenario A: [Specific Attack Name]
### Attack Concept: [Brief description]
**Objective**: [What attacker wants to achieve regardless of language/protocol]
**Scope**: [Single function, cross-function, system-wide, external]
**Complexity**: [Simple, moderate, advanced, expert-level]

### Phase 1: [Setup Phase] - Universal Preparation
[Detailed setup that adapts to any language/protocol]
- **Environment Setup**: [Attacker preparation steps]
- **Resource Preparation**: [Assets, accounts, tools needed]
- **Target Identification**: [How to identify vulnerable targets]
- **Code Examples**: [Language-specific implementation examples]

### Phase 2: [Execution Phase] - Attack Implementation
[Step-by-step attack execution across different languages]
- **Initial Trigger**: [How the attack begins]
- **Exploitation Chain**: [Sequence of malicious operations]
- **State Manipulation**: [How system state is corrupted]
- **Persistence Mechanisms**: [How attack maintains access/effect]

### Phase 3: [Exploitation Phase] - Value Extraction/Damage
[How to extract value or cause damage in any system]
- **Direct Benefits**: [Immediate gains for attacker]
- **Indirect Benefits**: [Secondary advantages gained]
- **System Damage**: [Harm caused to the target system]
- **Cleanup/Covering Tracks**: [How attacker hides evidence]

## Language-Specific Implementations
### For Solidity/EVM:
[Specific code examples and techniques for Ethereum]

### For Rust/Solana:
[Specific code examples and techniques for Solana]

### For Go/Cosmos:
[Specific code examples and techniques for Cosmos]

### For TypeScript/JavaScript:
[Specific code examples and techniques for web services]

### For Python:
[Specific code examples and techniques for Python services]

### For [Other Languages]:
[Adaptable template for any other language]

## Expected Vulnerabilities to Find (Universal)
[List of vulnerability types this scenario should uncover across any language]
- **Logic Vulnerabilities**: [Business logic flaws]
- **Access Control Issues**: [Permission and authentication flaws]
- **State Management Problems**: [Data consistency issues]
- **Resource Management Issues**: [Memory, CPU, network problems]
- **Integration Vulnerabilities**: [External system interaction flaws]
```

## Phase 5: Universal Comprehensive Vulnerability Documentation

### Step 5.1: Create Universal Vulnerability Report Structure
In `report-review/`, create (adapts to any codebase):
- `00-vulnerability-summary.md` (Executive summary for any protocol type)
- `01-critical-vulnerabilities.md` (Overview report with language-specific details)
- Individual CVE files: `CVE-XXX-[vulnerability-name].md` (Universal format)
- `comprehensive-vulnerability-test-suite.md` (Testing framework for any language)

**Report Structure Adapts To:**
- **Smart Contract Protocols** (Solidity, Rust, Vyper)
- **Web Services** (TypeScript, Python, Go, Java)
- **Mobile Applications** (Swift, Kotlin, React Native)
- **Desktop Applications** (C++, C#, Electron)
- **System Software** (C, Rust, Assembly)
- **Database Systems** (SQL, NoSQL, Graph databases)
- **Network Protocols** (Custom protocols, APIs)

### Step 5.2: Universal Individual Vulnerability Report Template
```markdown
# CVE-XXX: [Vulnerability Name] - Universal Analysis

## Finding Description and Impact (Language-Agnostic)
**Root Cause**: [Detailed description that applies across languages]
**Affected Languages**: [List of programming languages where this vulnerability exists]
**Affected Platforms**: [Blockchain platforms, operating systems, frameworks affected]
**Protocol Types**: [Types of protocols/applications vulnerable to this]

**Impact Assessment**:
- **Confidentiality**: [Information disclosure risks]
- **Integrity**: [Data corruption/manipulation risks]
- **Availability**: [Service disruption/DoS risks]
- **Financial Impact**: [Monetary losses possible]
- **Reputation Impact**: [Brand/trust damage potential]

## Step-by-Step Example of the Vulnerability (Universal)
### Normal Flow (Expected Behavior):
[How function/method/process should work normally across any language]
1. **Input Validation**: [Expected input checking]
2. **State Verification**: [Expected state consistency checks]
3. **Operation Execution**: [Expected core logic execution]
4. **State Update**: [Expected state modifications]
5. **Output Generation**: [Expected return values/responses]

### Vulnerable Exploit Flow (Attack Pattern):
[How attacker exploits the vulnerability regardless of language]
1. **Attack Preparation**: [How attacker sets up the exploit]
2. **Vulnerability Trigger**: [How the vulnerable code path is activated]
3. **Exploitation Chain**: [Sequence of malicious operations]
4. **State Corruption**: [How system state becomes compromised]
5. **Value Extraction**: [How attacker benefits from the exploit]

## Vulnerability Flow (Universal Implementation)
### Phase 1: [Setup] - Cross-Language Preparation
[Attack setup with examples for multiple languages]
- **Solidity Example**: [Smart contract exploit setup]
- **Rust Example**: [Rust/Solana program exploit setup]
- **TypeScript Example**: [Web service exploit setup]
- **Python Example**: [Python service exploit setup]
- **Go Example**: [Go service exploit setup]

### Phase 2: [Execution] - Attack Implementation
[Attack execution with language-specific examples]
- **Common Attack Pattern**: [Language-agnostic attack logic]
- **Language-Specific Variations**: [How attack adapts to different languages]
- **Platform-Specific Considerations**: [Blockchain vs traditional system differences]

### Phase 3: [Exploitation] - Value Extraction
[Value extraction methods across different systems]
- **Financial Extraction**: [How to extract monetary value]
- **Data Extraction**: [How to extract sensitive information]
- **Access Extraction**: [How to gain unauthorized access]
- **Disruption Execution**: [How to cause system damage]

## Recommended Mitigation Steps (Universal)
### 1. **[Primary Fix] - Core Solution**
[Main fix with examples for multiple languages]
- **Solidity Implementation**: [Smart contract fix]
- **Rust Implementation**: [Rust/Solana program fix]
- **TypeScript Implementation**: [Web service fix]
- **Python Implementation**: [Python service fix]
- **Universal Principles**: [Language-agnostic security principles]

### 2. **[Defense in Depth] - Additional Protections**
[Secondary protections applicable across languages]
- **Input Validation**: [Comprehensive input sanitization]
- **Access Control**: [Proper permission checking]
- **Rate Limiting**: [Request/operation throttling]
- **Monitoring**: [Attack detection and alerting]
- **Audit Logging**: [Comprehensive activity logging]

### 3. **[System-Level Protections]**
[Infrastructure and architectural improvements]
- **Network Security**: [Firewall, VPN, network segmentation]
- **Infrastructure Security**: [Container security, OS hardening]
- **Deployment Security**: [Secure CI/CD, environment isolation]
- **Monitoring Security**: [SIEM, intrusion detection]

## Proof of Concept (PoC) - Multi-Language
### Solidity PoC:
[Complete working test code for Ethereum]

### Rust PoC:
[Complete working test code for Solana]

### TypeScript PoC:
[Complete working test code for web services]

### Python PoC:
[Complete working test code for Python services]

### Universal Testing Framework:
[Language-agnostic testing approach and validation methods]
```

## Phase 6: Systematic Vulnerability Discovery

### Step 6.1: Analysis Methodology
Follow this systematic approach:

1. **Individual Function Analysis**
   - Line-by-line code review with realistic values
   - State change tracking with exact amounts
   - Vulnerability identification at each step
   - Integration point mapping

2. **Comparative Analysis**
   - Function pair comparisons for asymmetries
   - Error handling pattern differences
   - Operation order variations
   - Return type inconsistencies
   - Validation strength differences

3. **Scenario Testing**
   - Reverse flow testing (operations in unexpected order)
   - Multi-flow combinations (simultaneous operations)
   - Concurrent operation chaos (race conditions)
   - Temporal manipulation (time-based attacks)
   - Asymmetric exploitation (leveraging differences)

4. **Vulnerability Synthesis**
   - Chain individual vulnerabilities into attack vectors
   - Develop coordinated multi-vector attacks
   - Scale attacks across multiple accounts
   - Create comprehensive test suites

### Step 6.2: Critical Analysis Techniques

#### The "Realistic Scenario" Method (Universal):
Always use real-world amounts and scenarios appropriate to the system:

**For Financial Systems:**
- **AliceCool's $20K deposits** (not $100 test amounts)
- **BobWhale's $500K positions** (realistic whale trading)
- **CharlieBot's MEV extraction** (realistic arbitrage amounts)
- **Multi-user coordination** (5+ characters working together)
- **Gas-optimized attacks** (considering real network costs)

**For Gaming Systems:**
- **PlayerPete's $1.5K NFT purchases** (realistic gaming spending)
- **WhaleWanda's $150K collections** (realistic whale collections)
- **GamerGina's tournament stakes** (meaningful competition amounts)
- **Multi-guild coordination** (coordinated gaming attacks)
- **Real game economics** (actual token values and rewards)

**For Infrastructure Systems:**
- **DianaValidator's $100K stakes** (realistic validator bonds)
- **GovernorGary's governance proposals** (meaningful voting thresholds)
- **TreasuryTom's $1M treasury** (realistic protocol treasuries)
- **Multi-validator coordination** (realistic consensus attacks)
- **Real network resources** (actual bandwidth, storage, compute costs)

#### The "Developer Assumption Violation" Technique:
Systematically violate every assumption:
```
Assumption: Users deposit before trading
Violation: Trade before deposit, withdraw before deposit

Assumption: Operations happen sequentially
Violation: Execute simultaneously, recursively, in reverse

Assumption: Tokens behave predictably
Violation: Deploy malicious tokens with reentrancy, false returns

Assumption: Math is precise enough
Violation: Craft amounts that maximize precision loss

Assumption: Operators stay in their lanes
Violation: Escalate privileges through cross-function calls
```

#### The "Cross-Function Exploitation" Framework:
Never analyze functions in isolation:
1. **Map all function interactions**
2. **Identify shared state variables**
3. **Find reentrancy paths between functions**
4. **Test cross-function race conditions**
5. **Exploit function asymmetries**

#### The "Malicious Arsenal" Strategy (Universal):
Deploy different malicious components for different attacks across any system:

**For Smart Contracts (Solidity/Rust):**
```solidity
// EveExploiter's Phantom Balance Token: Always fails transferFrom
contract EvePhantomToken {
    function transferFrom(address, address, uint256) external returns (bool) {
        return false; // Fail transfer, keep internal credit for EveExploiter
    }
}

// HackerHank's Reentrancy Token: Triggers cross-function calls
contract HankReentrantToken {
    function transfer(address to, uint256 amount) external returns (bool) {
        if (msg.sender == accountManager) {
            CLOB(clob).postLimitOrder(hackerHank, maliciousArgs);
        }
        return true;
    }
}

// CharlieBot's Precision Bomb: Maximizes rounding errors
contract CharliePrecisionToken {
    uint256 public constant PRECISION_EXPLOIT = ***************;
}
```

**For Web Services (TypeScript/Python):**
```typescript
// EveExploiter's Malicious API Client
class EveApiExploiter {
    async exploitRateLimit() {
        // Flood API with requests from multiple IPs
        for (let i = 0; i < 1000000; i++) {
            this.makeRequest(`/api/data?id=${i}`);
        }
    }
}

// HackerHank's SQL Injection Payload
class HankSqlInjector {
    maliciousInput = "'; DROP TABLE users; --";

    async exploitDatabase(userInput: string) {
        // Inject malicious SQL through user input
        return await db.query(`SELECT * FROM users WHERE name = '${userInput}'`);
    }
}
```

**For Mobile Apps (Swift/Kotlin):**
```swift
// EveExploiter's Memory Bomb
class EveMemoryExploiter {
    func exhaustMemory() {
        var memoryBomb: [Data] = []
        while true {
            memoryBomb.append(Data(count: 1024 * 1024)) // 1MB chunks
        }
    }
}

// GrieferGabe's UI Manipulation
class GabeUIGriefer {
    func manipulateInterface() {
        // Overlay fake UI elements to trick users
        createFakeLoginScreen()
        interceptUserCredentials()
    }
}
```

### Step 6.2: Critical Vulnerability Categories to Find

#### Mathematical Precision Errors
- Integer overflow/underflow
- Rounding error accumulation
- Cross-decimal precision loss
- Division by zero scenarios

#### Race Conditions
- Concurrent operations on same resources
- Cross-function state corruption
- Temporal boundary races
- Mempool ordering exploitation

#### Access Control Bypasses
- Operator role escalation
- Cross-account privilege inheritance
- Reentrancy-based bypasses
- Context manipulation attacks

#### State Consistency Issues
- Internal vs external balance mismatches
- Cross-function state corruption
- Partial failure handling
- Atomic operation violations

#### MEV and Front-Running
- Predictable operation patterns
- Mempool visibility exploitation
- Temporal arbitrage opportunities
- Cross-function MEV extraction

### Step 6.3: Vulnerability Chaining Strategy
Always look for ways to chain vulnerabilities:
1. **Single vulnerability** → **Multi-vector attack**
2. **Individual function issue** → **Cross-function exploitation**
3. **Minor precision error** → **Systematic extraction**
4. **Simple reentrancy** → **Complex attack chains**

## Phase 7: Testing and Validation

### Step 7.1: Create Comprehensive Test Suite
Develop tests for:
- Each individual vulnerability
- Multi-vector attack combinations
- Edge case scenarios
- Mitigation effectiveness

### Step 7.2: Test Categories
1. **Unit Tests**: Individual vulnerability validation
2. **Integration Tests**: Cross-function attack chains
3. **Stress Tests**: System limits and breaking points
4. **Adversarial Tests**: Sophisticated attack scenarios

## Success Criteria

### Complete Success Indicators:
- ✅ **19+ vulnerabilities discovered** across all severity levels
- ✅ **Multi-vector attack chains** documented with working PoCs
- ✅ **Systematic fund extraction** methods identified
- ✅ **Complete protocol compromise** scenarios developed
- ✅ **Comprehensive test suite** validating all findings

### Quality Metrics:
- **Depth**: Line-by-line analysis with realistic values
- **Breadth**: All functions and interaction patterns covered
- **Innovation**: Novel attack vectors and vulnerability chains
- **Practicality**: Working exploits with measurable impact

## Phase 8: Advanced Analysis Techniques

### Step 8.1: Realistic Value Consistency
**CRITICAL**: Use the same realistic values across ALL analyses:

#### Standard User Personas and Values:
- **Alice (Retail Trader)**:
  - Deposits: $20,000 USDC (20,000,000,000 wei)
  - Orders: 5 ETH at $3,000 = $15,000 worth
  - Amendments: 3.2 ETH at $3,000 = $9,600 worth
  - Market orders: 2.7 ETH = $8,100 worth

- **Bob (Liquidity Provider)**:
  - Large deposits: $100,000+ USDC
  - Multiple orders across price levels
  - Frequent amendments and cancellations

- **Charlie (Arbitrageur)**:
  - High-frequency operations
  - Cross-market arbitrage amounts
  - MEV extraction scenarios

- **Eve (Attacker)**:
  - Malicious token deployments
  - Coordinated multi-account attacks
  - Systematic exploitation patterns

### Step 8.2: Code Analysis Depth Requirements

#### Line-by-Line Analysis Standards:
For every function, document:
```
Line [X]: [exact code from codebase]
├── Input Values: [specific realistic amounts]
├── Calculation: [step-by-step math with actual numbers]
├── State Before: [exact state values]
├── State After: [exact state values]
├── Gas Cost: [estimated gas consumption]
├── External Calls: [any external contract interactions]
├── Events Emitted: [specific events with parameters]
└── Vulnerability Check: [potential issues at this line]
```

#### Integration Point Mapping:
For each function, map:
- **Calls TO this function**: Which functions/contracts call it
- **Calls FROM this function**: Which functions/contracts it calls
- **Shared State**: What storage variables are modified
- **Event Dependencies**: What events other systems depend on
- **Reentrancy Paths**: All possible reentrancy entry points

### Step 8.3: Vulnerability Discovery Methodology

#### The "Developer Assumption Challenge" Framework:
For every function, challenge these assumptions:

1. **Sequential Execution Assumption**
   - Challenge: "What if operations happen simultaneously?"
   - Test: Execute functions concurrently, in reverse order, recursively

2. **Trusted Input Assumption**
   - Challenge: "What if inputs are malicious?"
   - Test: Malicious tokens, extreme values, edge cases

3. **State Consistency Assumption**
   - Challenge: "What if state becomes inconsistent?"
   - Test: Partial failures, reentrancy, race conditions

4. **Mathematical Precision Assumption**
   - Challenge: "What if calculations lose precision?"
   - Test: Large numbers, repeated operations, rounding errors

5. **Access Control Assumption**
   - Challenge: "What if roles are escalated?"
   - Test: Operator exploitation, cross-function privilege inheritance

#### The "Realistic Scenario Stress Test":
Every vulnerability must be tested with:
- **Real DeFi amounts**: $10K+ transactions
- **Multiple users**: 5+ coordinated accounts
- **Time pressure**: Block boundary conditions
- **Network conditions**: High gas, congestion, MEV

### Step 8.4: Multi-Vector Attack Development

#### Attack Chain Construction:
1. **Start with single vulnerability**
2. **Identify amplification opportunities**
3. **Chain with other vulnerabilities**
4. **Scale across multiple accounts**
5. **Optimize for maximum damage**

#### Example Chain Development:
```
Single Issue: Deposit credits before transfer
↓
Amplification: Use malicious token to fail transfer
↓
Chaining: Trigger reentrancy during deposit
↓
Scaling: Deploy across 100 accounts
↓
Optimization: Extract maximum value through trading
```

### Step 8.5: Documentation Standards

#### File Naming Convention:
- Flow analyses: `Flow XX: [function-name].md`
- Comparisons: `XX-[func1]-vs-[func2].md`
- Scenarios: `XX-[attack-category]-scenarios.md`
- Vulnerabilities: `CVE-XXX-[vulnerability-name].md`

#### Content Quality Requirements:
- **Realistic values**: Always use consistent DeFi amounts
- **Working code**: All examples must be executable
- **Measurable impact**: Quantify damage in dollars/tokens
- **Step-by-step flows**: No gaps in explanation
- **Visual clarity**: Use tables, lists, code blocks effectively

## Phase 9: Quality Assurance and Validation

### Step 9.1: Self-Validation Checklist
Before completing analysis, verify:

#### Completeness Check:
- [ ] All 6 core functions analyzed line-by-line
- [ ] All function pairs compared for asymmetries
- [ ] All 7 scenario categories explored
- [ ] 15+ vulnerabilities documented with PoCs
- [ ] Multi-vector attack chains developed
- [ ] Comprehensive test suite created

#### Quality Check:
- [ ] Consistent realistic values used throughout
- [ ] All code examples are executable
- [ ] All vulnerabilities have working PoCs
- [ ] All attack scenarios are practical
- [ ] All recommendations include code fixes

#### Innovation Check:
- [ ] Novel attack vectors discovered
- [ ] Sophisticated vulnerability chains developed
- [ ] Developer assumptions systematically challenged
- [ ] Edge cases and race conditions explored
- [ ] Cross-function exploitation documented

### Step 9.2: Impact Validation
Each vulnerability must demonstrate:
- **Measurable financial impact**: Quantified in tokens/USD
- **Practical exploitability**: Working attack code
- **Scalability**: Can be repeated/amplified
- **Stealth**: Can avoid detection initially
- **Persistence**: Creates lasting damage

## Phase 10: Continuous Improvement

### Step 10.1: Framework Evolution
After each analysis, update this prompt with:
- New vulnerability categories discovered
- Improved analysis techniques
- Better attack vector methodologies
- Enhanced testing frameworks

### Step 10.2: Methodology Refinement
Track and improve:
- **Discovery rate**: Vulnerabilities found per hour
- **Quality metrics**: Severity and exploitability
- **Innovation index**: Novel vs known vulnerability types
- **Practical impact**: Real-world applicability

## UNIVERSAL EXECUTION INSTRUCTIONS

### When Using This Prompt on ANY Codebase:

#### Step 1: Universal Initial Setup
1. **Create the exact folder structure** as specified in Phase 1
2. **Identify the 6 core functions/methods/instructions** in the target system (regardless of language)
3. **Select appropriate user personas** from our fun cast of characters based on protocol type:
   - **DeFi**: AliceCool, BobWhale, CharlieBot, DianaValidator, EveExploiter
   - **GameFi**: PlayerPete, WhaleWanda, GamerGina, CollectorCole, EveExploiter
   - **Infrastructure**: DianaValidator, GovernorGary, OperatorOllie, AdminAnna, HackerHank
   - **Web Services**: AliceCool, BobBuilder, TechTina, AdminAnna, EveExploiter
4. **Map all function/method interactions** and integration points
5. **Identify the programming language(s)** and adapt analysis techniques accordingly
6. **Understand the system architecture** (smart contracts, web services, mobile apps, etc.)

**Language-Specific Setup:**
- **Solidity/EVM**: Identify contracts, functions, modifiers, events
- **Rust/Solana**: Identify programs, instructions, accounts, PDAs
- **Go**: Identify packages, functions, methods, interfaces
- **TypeScript/JavaScript**: Identify modules, functions, classes, APIs
- **Python**: Identify modules, functions, classes, decorators
- **C/C++**: Identify functions, structs, memory management patterns
- **Java/C#**: Identify classes, methods, interfaces, annotations

#### Step 2: Universal Systematic Analysis
1. **Start with individual function/method flows** - analyze each function line-by-line with realistic values appropriate for the system type
2. **Create comparison analyses** - identify asymmetries between function/method pairs across any language
3. **Develop attack scenarios** - test all 7 scenario categories systematically, adapting to the specific technology stack
4. **Document vulnerabilities** - create individual CVE files for each finding with multi-language examples

**Analysis Adaptation by System Type:**
- **Financial Systems**: Focus on monetary flows, precision, access control
- **Gaming Systems**: Focus on asset management, fairness, anti-cheat mechanisms
- **Infrastructure Systems**: Focus on availability, scalability, resource management
- **Social Systems**: Focus on privacy, data integrity, user interactions
- **IoT Systems**: Focus on device security, communication protocols, resource constraints

#### Step 3: Advanced Exploitation
1. **Chain vulnerabilities together** - develop multi-vector attacks
2. **Scale attacks across accounts** - test coordinated exploitation
3. **Create comprehensive PoCs** - working code for all vulnerabilities
4. **Build test suites** - validate all findings with executable tests

#### Step 4: Quality Validation
1. **Verify realistic values** - ensure consistency across all analyses
2. **Test all code examples** - confirm executability of all PoCs
3. **Quantify impact** - measure financial damage in real terms
4. **Document systematically** - follow exact naming and structure conventions

### Universal Critical Success Factors:

#### Must-Have Characteristics (Adapts to Any System):
- ✅ **Realistic amounts**: Always use significant values appropriate for the system type
  - **Financial Systems**: $10K+ transaction sizes
  - **Gaming Systems**: Valuable in-game assets (rare NFTs, high-level items)
  - **Infrastructure Systems**: Production-level resource usage
  - **Social Systems**: Real user data volumes and interaction patterns
- ✅ **Consistent fun personas**: Same memorable characters across all analyses
  - **AliceCool & BobWhale**: For user vs power-user scenarios
  - **CharlieBot & TechTina**: For automated/bot operations
  - **EveExploiter & HackerHank**: For malicious actor testing
  - **DianaValidator & AdminAnna**: For privileged operations
- ✅ **Working attack code**: All vulnerabilities must have executable PoCs in the target language
- ✅ **Multi-vector chains**: Individual issues must be chained together regardless of language
- ✅ **Measurable impact**: Quantify damage in appropriate units (money, data, access, availability)
- ✅ **Systematic coverage**: All functions/methods and interactions analyzed comprehensively

#### Quality Indicators:
- **15+ vulnerabilities discovered** across all severity levels
- **Multi-account coordination** in attack scenarios
- **Cross-function exploitation** chains documented
- **Temporal and race condition** attacks developed
- **Mathematical precision** vulnerabilities found
- **Complete protocol compromise** scenarios created

### Expected Deliverables:

#### File Structure Output:
```
aarc/
├── flow-digest/
│   ├── Flow 01: [function].md
│   ├── Flow 02: [function].md
│   ├── Flow 03: [function].md
│   ├── Flow 04: [function].md
│   ├── Flow 05: [function].md
│   ├── Flow 06: [function].md
│   ├── comprehensive-scenario.md
│   └── multi-user-scenario.md
├── compare-flow-digest/
│   ├── 01-[func1]-vs-[func2].md
│   ├── 02-[func3]-vs-[func4].md
│   └── 03-[func5]-vs-[func6].md
├── scenarios/
│   ├── 01-reverse-flow-scenarios.md
│   ├── 02-malicious-user-scenarios.md
│   ├── 03-multi-flow-attack-scenarios.md
│   ├── 04-concurrent-operation-chaos.md
│   ├── 05-asymmetric-exploitation-patterns.md
│   ├── 06-temporal-attack-vectors.md
│   └── 07-ultimate-protocol-stress-test.md
├── report-review/
│   ├── 00-vulnerability-summary.md
│   ├── 01-critical-vulnerabilities.md
│   ├── CVE-001-[vulnerability].md
│   ├── CVE-002-[vulnerability].md
│   ├── [... individual CVE files ...]
│   └── comprehensive-vulnerability-test-suite.md
├── README.md
└── complete-prompt.md
```

#### Universal Minimum Vulnerability Categories (Adapts to Any System):

**For Financial/DeFi Systems:**
- **Asset Manipulation** (phantom balances, double spending)
- **Mathematical Precision Errors** (rounding, overflow, underflow)
- **Cross-Function Reentrancy** (complex attack chains)
- **MEV Extraction** (front-running, sandwich attacks)
- **Access Control Bypass** (privilege escalation)

**For Gaming/NFT Systems:**
- **Asset Duplication** (item cloning, NFT copying)
- **Game Logic Manipulation** (score tampering, unfair advantages)
- **Reward System Exploitation** (unearned rewards, infinite generation)
- **Marketplace Manipulation** (price manipulation, fake listings)
- **Anti-Cheat Bypass** (detection avoidance, bot automation)

**For Infrastructure/Governance Systems:**
- **Consensus Manipulation** (vote buying, delegation attacks)
- **Resource Exhaustion** (DoS attacks, resource monopolization)
- **Upgrade Exploitation** (malicious upgrades, rollback attacks)
- **Validator Attacks** (slashing avoidance, double signing)
- **Network Partition** (split-brain scenarios, isolation attacks)

**Universal Categories (Apply to All Systems):**
- **Race Conditions** (concurrent operations, timing attacks)
- **State Consistency Issues** (partial failures, data corruption)
- **Input Validation Bypass** (injection attacks, malformed data)
- **Authentication/Authorization Flaws** (identity spoofing, privilege escalation)
- **Resource Management Issues** (memory leaks, CPU exhaustion)
- **Integration Vulnerabilities** (external system exploitation)
- **Temporal Manipulation** (time-based attacks, sequence exploitation)

### Universal Final Validation:

Before considering the analysis complete for ANY codebase, ensure:

#### **Core Analysis Completeness:**
1. **All 6 core functions/methods** analyzed with realistic scenarios appropriate to system type
2. **All function/method pairs** compared for asymmetries across any language
3. **All 7 scenario categories** explored systematically, adapted to the technology stack
4. **15+ vulnerabilities** documented with working PoCs in the target language(s)
5. **Multi-vector attacks** chaining vulnerabilities together regardless of platform
6. **Comprehensive test suite** validating all findings in the appropriate testing framework
7. **Complete system compromise** scenarios developed for maximum impact assessment

#### **Language-Specific Validation:**
- **Smart Contracts**: Gas optimization attacks, reentrancy chains, economic exploits
- **Web Services**: Injection attacks, authentication bypasses, API abuse
- **Mobile Apps**: Platform-specific exploits, data leakage, privilege escalation
- **Desktop Apps**: Memory corruption, file system attacks, process injection
- **System Software**: Buffer overflows, race conditions, privilege escalation

#### **Universal Quality Metrics:**
- **Depth**: Line-by-line analysis with language-appropriate detail
- **Breadth**: All critical functions and interaction patterns covered
- **Innovation**: Novel attack vectors specific to the technology stack
- **Practicality**: Working exploits with measurable impact in the system context
- **Adaptability**: Techniques that work across similar systems and languages

This universal framework guarantees systematic discovery of critical vulnerabilities that developers never anticipated, using sophisticated attack vectors that demonstrate real-world exploitability across ANY programming language, platform, or protocol type. The methodology adapts seamlessly from Ethereum smart contracts to Solana programs, from TypeScript web services to Python APIs, from mobile applications to embedded systems.

---

## 🎭 **Quick Persona Reference Guide**

### 👥 **The Good Guys:**
- **AliceCool** 🌟: Your everyday user, realistic amounts, normal behavior
- **BobWhale** 🐋: The big spender, large volumes, major impact
- **BobBuilder** 🔨: The developer, integration testing, technical scenarios
- **DianaValidator** ⚖️: The network guardian, consensus operations, high stakes
- **AdminAnna** 👮‍♀️: The protocol admin, emergency powers, system control

### 🤖 **The Bots:**
- **CharlieBot** 🤖: Trading automation, MEV extraction, high-frequency ops
- **RobotRita** 🔄: Workflow automation, scheduled operations, systematic behavior
- **TechTina** 💻: API heavy user, programmatic interactions, integration testing

### 🎮 **The Gamers:**
- **PlayerPete** 🎮: Casual gaming, moderate spending, regular gameplay
- **WhaleWanda** 🐋🎮: Gaming whale, premium items, high-value collections
- **GamerGina** 🏆: Competitive player, tournaments, skill-based rewards
- **CollectorCole** 🃏: NFT collector, rare items, marketplace activity

### 😈 **The Bad Guys:**
- **EveExploiter** 😈: Primary attacker, vulnerability exploitation, systematic attacks
- **HackerHank** 💀: Advanced attacker, sophisticated exploits, multi-vector chains
- **GrieferGabe** 😠: Disruptor, griefing attacks, system abuse
- **ScammerSue** 🎭: Social engineer, phishing attacks, user deception

### 🏛️ **The Officials:**
- **GovernorGary** 🗳️: Governance operations, proposals, voting coordination
- **TreasuryTom** 🏛️: Treasury management, large fund operations, DAO activities
- **OperatorOllie** ⚙️: Day-to-day operations, maintenance, system upkeep
- **ModeratorMax** 🛡️: Content moderation, user management, community safety

### 💡 **Usage Tips:**
- **Pick 5-7 relevant personas** for your protocol type
- **Use consistent amounts** for each character across all analyses
- **Make scenarios realistic** - no $100 test amounts, use real-world values
- **Create memorable stories** - "BobWhale's $500K position" is more engaging than "User B's large trade"
- **Chain character interactions** - "EveExploiter targets BobWhale's position using CharlieBot's MEV strategy"

**Remember**: These characters make vulnerability analysis more engaging, memorable, and realistic. Use them consistently across all your analyses to create compelling attack narratives that demonstrate real-world impact! 🎯
