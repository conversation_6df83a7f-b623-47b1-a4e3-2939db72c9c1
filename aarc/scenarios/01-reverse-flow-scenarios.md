# Scenario 01: Reverse Flow Attack Scenarios

## Overview

This document explores scenarios where we deliberately break the expected flow sequence to test system resilience. Instead of following the normal pattern (deposit → postLimitOrder → amend → postFillOrder → cancel → withdraw), we'll try unexpected sequences to uncover vulnerabilities.

## Scenario A: Withdraw Before Deposit

### Expected Flow: 
`deposit → trade → withdraw`

### Attack Flow: 
`withdraw → deposit → exploit`

### Test Case A1: Zero Balance Withdrawal
```solidity
// Alice has never deposited anything
accountTokenBalances[Alice][USDC] = 0;

// Alice tries to withdraw
withdraw(Alice, USDC, 1000);
// Expected: Should revert with BalanceInsufficient
// Actual: Let's test if there are any bypasses
```

**Potential Vulnerabilities:**
- Integer underflow if balance check is bypassed
- Operator role allowing withdrawal from zero balance
- Storage corruption allowing negative balances

### Test Case A2: Malicious Operator Withdrawal
```solidity
// Alice authorizes <PERSON> as operator
Alice.setOperator(<PERSON>, SPOT_WITHDRAW);

// <PERSON> tries to withdraw from Alice's zero balance
withdraw(Alice, USDC, 1000000);
// Expected: Should revert
// Test: Can operators bypass balance checks?
```

## Scenario B: Trade Before Deposit

### Expected Flow: 
`deposit → postLimitOrder`

### Attack Flow: 
`postLimitOrder → deposit → exploit`

### Test Case B1: Phantom Order Placement
```solidity
// Alice has zero balance
accountTokenBalances[Alice][USDC] = 0;

// Alice tries to place limit order
postLimitOrder(Alice, PostLimitOrderArgs({
    amountInBase: 5 ether,
    price: 3000 ether,
    side: Side.BUY
}));
// Expected: Should fail due to insufficient balance
// Test: Are balance checks enforced during order placement?
```

### Test Case B2: Fill Order Without Balance
```solidity
// Alice tries market order with zero balance
postFillOrder(Alice, PostFillOrderArgs({
    amount: 2.5 ether,
    priceLimit: 3300 ether,
    side: Side.BUY
}));
// Expected: Should fail
// Test: Can fill orders execute without sufficient balance?
```

## Scenario C: Cancel Non-Existent Orders

### Expected Flow: 
`postLimitOrder → cancel`

### Attack Flow: 
`cancel → postLimitOrder → exploit`

### Test Case C1: Cancel Random Order IDs
```solidity
// Alice tries to cancel orders she never placed
uint256[] memory fakeOrderIds = [999999, 888888, 777777];
cancel(Alice, CancelArgs(fakeOrderIds));
// Expected: Should emit CancelFailed events
// Test: Can this be exploited for gas griefing or other attacks?
```

### Test Case C2: Cancel Other Users' Orders
```solidity
// Bob placed Order ID 12345
// Alice tries to cancel Bob's order
uint256[] memory bobsOrders = [12345];
cancel(Alice, CancelArgs(bobsOrders));
// Expected: Should revert with CancelUnauthorized
// Test: Are ownership checks bulletproof?
```

## Scenario D: Amend Non-Existent Orders

### Expected Flow: 
`postLimitOrder → amend`

### Attack Flow: 
`amend → postLimitOrder → exploit`

### Test Case D1: Amend Random Order ID
```solidity
// Alice tries to amend order that doesn't exist
amend(Alice, AmendArgs({
    orderId: 999999,
    amountInBase: 3 ether,
    price: 3200 ether,
    side: Side.BUY,
    limitOrderType: LimitOrderType.POST_ONLY
}));
// Expected: Should revert with OrderNotFound
// Test: Any bypasses or edge cases?
```

### Test Case D2: Amend Other Users' Orders
```solidity
// Bob's Order ID 12345
// Alice tries to amend it
amend(Alice, AmendArgs({
    orderId: 12345,  // Bob's order
    amountInBase: 1 ether,
    price: 2000 ether,
    side: Side.BUY,
    limitOrderType: LimitOrderType.POST_ONLY
}));
// Expected: Should revert with AmendUnauthorized
// Test: Cross-user order manipulation possible?
```

## Scenario E: Multiple Deposits Without Trading

### Expected Flow: 
`deposit → trade → withdraw`

### Attack Flow: 
`deposit → deposit → deposit → exploit`

### Test Case E1: Overflow Attack Through Repeated Deposits
```solidity
// Alice deposits maximum amounts repeatedly
for (uint i = 0; i < 100; i++) {
    deposit(Alice, USDC, type(uint256).max / 100);
}
// Expected: Should handle large balances correctly
// Test: Integer overflow in balance calculations?
```

### Test Case E2: Reentrancy Through Malicious Token
```solidity
contract MaliciousUSDC {
    uint256 public reentrancyCount;
    
    function transferFrom(address from, address to, uint256 amount) external returns (bool) {
        if (reentrancyCount < 5) {
            reentrancyCount++;
            // Reenter deposit during transferFrom
            AccountManager(to).deposit(from, address(this), amount);
        }
        return true;
    }
}
```

## Scenario F: Concurrent Operations Chaos

### Expected Flow: 
Sequential operations

### Attack Flow: 
Simultaneous conflicting operations

### Test Case F1: Simultaneous Amend and Cancel
```solidity
// Alice places order
uint256 orderId = postLimitOrder(Alice, args);

// In same block:
// Transaction 1: amend(Alice, amendArgs)
// Transaction 2: cancel(Alice, [orderId])
// Test: Which operation wins? Is state consistent?
```

### Test Case F2: Deposit and Withdraw Race
```solidity
// Alice has 1000 USDC balance
// In same block:
// Transaction 1: withdraw(Alice, USDC, 1000)
// Transaction 2: deposit(Alice, USDC, 500)
// Test: Final balance consistency?
```

## Scenario G: Extreme Parameter Testing

### Test Case G1: Zero Amount Operations
```solidity
deposit(Alice, USDC, 0);
withdraw(Alice, USDC, 0);
postLimitOrder(Alice, PostLimitOrderArgs({amountInBase: 0, ...}));
// Test: How does system handle zero amounts?
```

### Test Case G2: Maximum Value Operations
```solidity
deposit(Alice, USDC, type(uint256).max);
postLimitOrder(Alice, PostLimitOrderArgs({
    amountInBase: type(uint256).max,
    price: type(uint256).max,
    ...
}));
// Test: Integer overflow protection?
```

## Scenario H: Cross-Function State Corruption

### Test Case H1: Deposit During Order Execution
```solidity
// Alice has pending fill order
// During order matching, Alice deposits more tokens
// Test: Does this affect order execution calculations?
```

### Test Case H2: Withdraw During Amendment
```solidity
// Alice amends order (increases size)
// Simultaneously withdraws the additional funds needed
// Test: Can this create inconsistent state?
```

## Expected Vulnerabilities to Find

### 1. **State Inconsistency**
- Operations completing in unexpected order
- Balance calculations becoming incorrect
- Order book corruption

### 2. **Access Control Bypasses**
- Operator privileges being exploited
- Cross-user operation authorization
- Role escalation attacks

### 3. **Integer Arithmetic Issues**
- Overflow/underflow in unexpected scenarios
- Division by zero in edge cases
- Precision loss in calculations

### 4. **Reentrancy Vulnerabilities**
- External calls allowing recursive operations
- State changes during external calls
- Cross-function reentrancy

### 5. **Gas Griefing**
- Operations consuming excessive gas
- DoS attacks through resource exhaustion
- Block gas limit exploitation

## Testing Framework

```solidity
contract ReverseFlowTests {
    function testWithdrawBeforeDeposit() external;
    function testTradeBeforeDeposit() external;
    function testCancelNonExistentOrders() external;
    function testAmendNonExistentOrders() external;
    function testConcurrentOperations() external;
    function testExtremeParameters() external;
    function testCrossFunctionCorruption() external;
}
```

These reverse flow scenarios are designed to break assumptions and uncover edge cases that normal usage patterns wouldn't reveal.
