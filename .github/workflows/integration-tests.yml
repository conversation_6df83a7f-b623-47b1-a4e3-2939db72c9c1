name: Integration Tests

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: nightly

      - name: Install dependencies
        run: forge install

      - name: Check contract sizes
        run: forge build --sizes

      - name: Run tests
        run: forge test
        env:
          FOUNDRY_PROFILE: integration
          FORK_URL: ${{ secrets.FORK_URL }}
