AllowanceTransferInvariants:invariant_balanceEqualsSpent() (runs: 256, calls: 3840, reverts: 879)
AllowanceTransferInvariants:invariant_permit2NeverHoldsBalance() (runs: 256, calls: 3840, reverts: 878)
AllowanceTransferInvariants:invariant_spendNeverExceedsPermit() (runs: 256, calls: 3840, reverts: 881)
AllowanceTransferTest:testApprove() (gas: 47570)
AllowanceTransferTest:testBatchTransferFrom() (gas: 159197)
AllowanceTransferTest:testBatchTransferFromDifferentOwners() (gas: 235094)
AllowanceTransferTest:testBatchTransferFromMultiToken() (gas: 231841)
AllowanceTransferTest:testBatchTransferFromWithGasSnapshot() (gas: 159857)
AllowanceTransferTest:testExcessiveInvalidation() (gas: 64205)
AllowanceTransferTest:testInvalidateMultipleNonces() (gas: 83150)
AllowanceTransferTest:testInvalidateNonces() (gas: 62847)
AllowanceTransferTest:testInvalidateNoncesInvalid() (gas: 16327)
AllowanceTransferTest:testLockdown() (gas: 145984)
AllowanceTransferTest:testLockdownEvent() (gas: 117749)
AllowanceTransferTest:testMaxAllowance() (gas: 134888)
AllowanceTransferTest:testMaxAllowanceDirtyWrite() (gas: 117455)
AllowanceTransferTest:testPartialAllowance() (gas: 105140)
AllowanceTransferTest:testReuseOrderedNonceInvalid() (gas: 69154)
AllowanceTransferTest:testSetAllowance() (gas: 89627)
AllowanceTransferTest:testSetAllowanceBatch() (gas: 133740)
AllowanceTransferTest:testSetAllowanceBatchDifferentNonces() (gas: 118603)
AllowanceTransferTest:testSetAllowanceBatchDirtyWrite() (gas: 99210)
AllowanceTransferTest:testSetAllowanceBatchEvent() (gas: 116049)
AllowanceTransferTest:testSetAllowanceCompactSig() (gas: 89587)
AllowanceTransferTest:testSetAllowanceDeadlinePassed() (gas: 56512)
AllowanceTransferTest:testSetAllowanceDirtyWrite() (gas: 72175)
AllowanceTransferTest:testSetAllowanceIncorrectSigLength() (gas: 29198)
AllowanceTransferTest:testSetAllowanceInvalidSignature() (gas: 64065)
AllowanceTransferTest:testSetAllowanceTransfer() (gas: 103115)
AllowanceTransferTest:testSetAllowanceTransferDirtyNonceDirtyTransfer() (gas: 97194)
AllowanceTransferTest:testTransferFromWithGasSnapshot() (gas: 132867)
AllowanceUnitTest:testPackAndUnpack(uint160,uint48,uint48) (runs: 256, μ: 39025, ~: 39103)
AllowanceUnitTest:testUpdateAllRandomly(uint160,uint48,uint48) (runs: 256, μ: 40243, ~: 40244)
AllowanceUnitTest:testUpdateAmountExpirationRandomly(uint160,uint48) (runs: 256, μ: 39169, ~: 39170)
CompactSignature:testCompactSignature27() (gas: 300)
CompactSignature:testCompactSignature28() (gas: 144)
DeployPermit2Test:testAllowanceTransferSanityCheck() (gas: 101876)
DeployPermit2Test:testDeployPermit2() (gas: 4337527)
DeployPermit2Test:testSignatureTransferSanityCheck() (gas: 92792)
EIP712Test:testDomainSeparator() (gas: 5881)
EIP712Test:testDomainSeparatorAfterFork() (gas: 10830)
MockPermit2Lib:testPermit2Code(address):(bool) (runs: 256, μ: 3003, ~: 3016)
NonceBitmapTest:testHighNonces() (gas: 36305)
NonceBitmapTest:testInvalidateFullWord() (gas: 63061)
NonceBitmapTest:testInvalidateNoncesRandomly(uint248,uint256) (runs: 256, μ: 30439, ~: 31139)
NonceBitmapTest:testInvalidateNonzeroWord() (gas: 85642)
NonceBitmapTest:testInvalidateTwoNoncesRandomly(uint248,uint256,uint256) (runs: 256, μ: 39182, ~: 39182)
NonceBitmapTest:testLowNonces() (gas: 41041)
NonceBitmapTest:testNonceWordBoundary() (gas: 42284)
NonceBitmapTest:testUseTwoRandomNonces(uint256,uint256) (runs: 256, μ: 49190, ~: 51625)
NonceBitmapTest:testUsingNonceTwiceFails(uint256) (runs: 256, μ: 21935, ~: 21960)
Permit2LibTest:testOZSafePermit() (gas: 24682)
Permit2LibTest:testOZSafePermitPlusOZSafeTransferFrom() (gas: 129329)
Permit2LibTest:testOZSafeTransferFrom() (gas: 39007)
Permit2LibTest:testPermit2() (gas: 22941)
Permit2LibTest:testPermit2DSLessToken() (gas: 7143)
Permit2LibTest:testPermit2DSMore32Token() (gas: 7252)
Permit2LibTest:testPermit2DSMoreToken() (gas: 7023)
Permit2LibTest:testPermit2Full() (gas: 42356)
Permit2LibTest:testPermit2InvalidAmount() (gas: 21011)
Permit2LibTest:testPermit2LargerDS() (gas: 51464)
Permit2LibTest:testPermit2LargerDSRevert() (gas: 32841)
Permit2LibTest:testPermit2NonPermitFallback() (gas: 37245)
Permit2LibTest:testPermit2NonPermitToken() (gas: 32164)
Permit2LibTest:testPermit2PlusTransferFrom2() (gas: 126995)
Permit2LibTest:testPermit2PlusTransferFrom2WithNonPermit() (gas: 148221)
Permit2LibTest:testPermit2PlusTransferFrom2WithNonPermitFallback() (gas: 174749)
Permit2LibTest:testPermit2PlusTransferFrom2WithWETH9Mainnet() (gas: 147934)
Permit2LibTest:testPermit2SmallerDS() (gas: 77688)
Permit2LibTest:testPermit2SmallerDSNoRevert() (gas: 59324)
Permit2LibTest:testPermit2WETH9Mainnet() (gas: 28774)
Permit2LibTest:testSimplePermit2() (gas: 29117)
Permit2LibTest:testSimplePermit2InvalidAmount() (gas: 16944)
Permit2LibTest:testSimplePermit2PlusTransferFrom2WithNonPermit() (gas: 148463)
Permit2LibTest:testStandardPermit() (gas: 22535)
Permit2LibTest:testStandardTransferFrom() (gas: 38143)
Permit2LibTest:testTransferFrom2() (gas: 38734)
Permit2LibTest:testTransferFrom2Full() (gas: 53368)
Permit2LibTest:testTransferFrom2InvalidAmount() (gas: 12732)
Permit2LibTest:testTransferFrom2NonPermitToken() (gas: 53170)
SignatureTransferTest:testCorrectWitnessTypehashes() (gas: 3091)
SignatureTransferTest:testGasMultiplePermitBatchTransferFrom() (gas: 270972)
SignatureTransferTest:testGasSinglePermitBatchTransferFrom() (gas: 183860)
SignatureTransferTest:testGasSinglePermitTransferFrom() (gas: 123854)
SignatureTransferTest:testInvalidateUnorderedNonces() (gas: 41396)
SignatureTransferTest:testPermitBatchMultiPermitSingleTransfer() (gas: 133675)
SignatureTransferTest:testPermitBatchTransferFrom() (gas: 162019)
SignatureTransferTest:testPermitBatchTransferFromSingleRecipient() (gas: 187957)
SignatureTransferTest:testPermitBatchTransferFromTypedWitness() (gas: 239926)
SignatureTransferTest:testPermitBatchTransferFromTypedWitnessInvalidType() (gas: 84489)
SignatureTransferTest:testPermitBatchTransferFromTypedWitnessInvalidTypeHash() (gas: 86007)
SignatureTransferTest:testPermitBatchTransferFromTypedWitnessInvalidWitness() (gas: 85751)
SignatureTransferTest:testPermitBatchTransferInvalidAmountsLengthMismatch() (gas: 41574)
SignatureTransferTest:testPermitBatchTransferMultiAddr() (gas: 160547)
SignatureTransferTest:testPermitBatchTransferSingleRecipientManyTokens() (gas: 209422)
SignatureTransferTest:testPermitTransferFrom() (gas: 92909)
SignatureTransferTest:testPermitTransferFromCompactSig() (gas: 124059)
SignatureTransferTest:testPermitTransferFromIncorrectSigLength() (gas: 51346)
SignatureTransferTest:testPermitTransferFromInvalidNonce() (gas: 72928)
SignatureTransferTest:testPermitTransferFromRandomNonceAndAmount(uint256,uint128) (runs: 256, μ: 95752, ~: 96728)
SignatureTransferTest:testPermitTransferFromToSpender() (gas: 93283)
SignatureTransferTest:testPermitTransferFromTypedWitness() (gas: 125159)
SignatureTransferTest:testPermitTransferFromTypedWitnessInvalidType() (gas: 55947)
SignatureTransferTest:testPermitTransferFromTypedWitnessInvalidTypehash() (gas: 56879)
SignatureTransferTest:testPermitTransferSpendLessThanFull(uint256,uint128) (runs: 256, μ: 97604, ~: 99733)
TypehashGeneration:testPermitBatch() (gas: 40473)
TypehashGeneration:testPermitBatchTransferFrom() (gas: 49837)
TypehashGeneration:testPermitBatchTransferFromWithWitness() (gas: 56621)
TypehashGeneration:testPermitBatchTransferFromWithWitnessIncorrectPermitData() (gas: 56744)
TypehashGeneration:testPermitBatchTransferFromWithWitnessIncorrectTypehashStub() (gas: 57353)
TypehashGeneration:testPermitSingle() (gas: 28138)
TypehashGeneration:testPermitTransferFrom() (gas: 36511)
TypehashGeneration:testPermitTransferFromWithWitness() (gas: 43469)
TypehashGeneration:testPermitTransferFromWithWitnessIncorrectPermitData() (gas: 43436)
TypehashGeneration:testPermitTransferFromWithWitnessIncorrectTypehashStub() (gas: 43956)